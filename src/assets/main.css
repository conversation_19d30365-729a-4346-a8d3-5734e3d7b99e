@import './base.css';

#app {
  margin: 0 auto;
  font-weight: normal;
}

body {
  background-color: #f8f9fa;
  margin: 0;
  padding: 0;
}

/* 确保所有页面内容都有正确的顶部间距 */
.page-content {
  padding-top: 80px;
}

/* 滚动行为优化 */
html {
  scroll-behavior: smooth;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}
