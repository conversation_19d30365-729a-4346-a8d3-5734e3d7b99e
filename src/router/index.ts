import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '@/views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/books/well-grounded-java-developer',
      name: 'book-introduction',
      component: () => import('@/views/BookIntroduction.vue'),
    },
    {
      path: '/chapter1',
      name: 'chapter1',
      component: () => import('@/views/JavaChapter1.vue'),
    },
    {
      path: '/chapter2',
      name: 'chapter2',
      component: () => import('@/views/JavaChapter2.vue'),
    },
    {
      path: '/chapter3',
      name: 'chapter3',
      component: () => import('@/views/JavaChapter3.vue'),
    },
    {
      path: '/chapter4',
      name: 'chapter4',
      component: () => import('@/views/JavaChapter4.vue'),
    },
    {
      path: '/chapter5',
      name: 'chapter5',
      component: () => import('@/views/JavaChapter5.vue'),
    },
    {
      path: '/chapter6',
      name: 'chapter6',
      component: () => import('@/views/JavaChapter6.vue'),
    },
    {
      path: '/chapter7',
      name: 'chapter7',
      component: () => import('@/views/JavaChapter7.vue'),
    },
    {
      path: '/chapter8',
      name: 'chapter8',
      component: () => import('@/views/JavaChapter8.vue'),
    },
    {
      path: '/chapter9',
      name: 'chapter9',
      component: () => import('@/views/JavaChapter9.vue'),
    },
    {
      path: '/chapter10',
      name: 'chapter10',
      component: () => import('@/views/JavaChapter10.vue'),
    },
    {
      path: '/chapter11',
      name: 'chapter11',
      component: () => import('@/views/JavaChapter11.vue'),
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('@/views/AboutView.vue'),
    },
  ],
})

export default router
