<template>
  <div class="book-introduction">
    <!-- 书籍头部信息 -->
    <section class="book-hero">
      <div class="container">
        <div class="book-hero-content">
          <div class="book-cover">
            <div class="book-cover-placeholder">📖</div>
          </div>
          <div class="book-details">
            <h1 class="book-title">The Well-Grounded Java Developer</h1>
            <p class="book-subtitle">Second Edition</p>
            <div class="book-meta">
              <div class="meta-item">
                <span class="meta-label">作者:</span>
                <span class="meta-value"><PERSON>, <PERSON><PERSON><PERSON>, <PERSON></span>
              </div>
              <div class="meta-item">
                <span class="meta-label">出版社:</span>
                <span class="meta-value">Manning Publications</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">出版年份:</span>
                <span class="meta-value">2022</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">页数:</span>
                <span class="meta-value">688页</span>
              </div>
            </div>
            <div class="book-actions">
              <RouterLink to="/chapter1" class="start-reading-btn"> 开始阅读 → </RouterLink>
              <button class="bookmark-btn" @click="toggleBookmark">
                {{ isBookmarked ? '已收藏' : '收藏' }} ⭐
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 书籍简介 -->
    <section class="book-description">
      <div class="container">
        <h2>📝 书籍简介</h2>
        <div class="description-content">
          <p>
            《The Well-Grounded Java Developer, Second Edition》是一本全面深入的Java开发指南，
            专为希望掌握现代Java开发技术的程序员而写。本书涵盖了从Java基础到高级特性的全方位内容。
          </p>
          <p>
            第二版更新了Java 17的最新特性，包括模块系统、新的语言特性、性能优化技术等。
            通过实际案例和深入分析，帮助读者建立扎实的Java开发基础。
          </p>
        </div>
      </div>
    </section>

    <!-- 章节目录 -->
    <section class="chapters-overview">
      <div class="container">
        <h2>📚 章节目录</h2>
        <div class="chapters-grid">
          <div v-for="chapter in chapters" :key="chapter.id" class="chapter-card">
            <div class="chapter-header">
              <div class="chapter-number">{{ chapter.id }}</div>
              <h3 class="chapter-title">{{ chapter.title }}</h3>
            </div>
            <p class="chapter-description">{{ chapter.description }}</p>
            <div class="chapter-topics">
              <span v-for="topic in chapter.topics" :key="topic" class="topic-tag">
                {{ topic }}
              </span>
            </div>
            <RouterLink :to="chapter.path" class="chapter-link"> 学习本章 → </RouterLink>
          </div>
        </div>
      </div>
    </section>

    <!-- 学习特色 -->
    <section class="learning-features">
      <div class="container">
        <h2>✨ 学习特色</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🎯</div>
            <h3>互动式学习</h3>
            <p>通过可交互的代码示例和实时演示，让学习更加生动有趣</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📊</div>
            <h3>可视化图表</h3>
            <p>使用Mermaid图表和动画，直观展示复杂的概念和流程</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">💻</div>
            <h3>代码实践</h3>
            <p>内置代码编辑器，支持实时编译和运行Java代码</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🔍</div>
            <h3>深度解析</h3>
            <p>深入JVM底层原理，理解Java程序的执行机制</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const isBookmarked = ref(false)

const chapters = [
  {
    id: 1,
    title: 'Introducing Modern Java',
    description: '了解Java语言与平台的区别，新的发布模型，var关键字，以及Java 11的重要变更',
    path: '/chapter1',
    topics: ['Java平台', '发布模型', 'var关键字', 'Java 11'],
  },
  {
    id: 2,
    title: 'Java Modules (JPMS)',
    description: '深入理解Java平台模块系统，掌握模块化架构设计和jlink工具的使用',
    path: '/chapter2',
    topics: ['模块系统', 'JPMS', 'jlink', '模块化设计'],
  },
  {
    id: 3,
    title: 'Java 17 现代特性',
    description: '探索文本块、Switch表达式、Records、密封类型与模式匹配等现代Java特性',
    path: '/chapter3',
    topics: ['文本块', 'Switch表达式', 'Records', '密封类型', '模式匹配'],
  },
  {
    id: 4,
    title: 'Class Files & Bytecode',
    description: '深入JVM底层：类加载机制、字节码指令与反射原理',
    path: '/chapter4',
    topics: ['类加载', '字节码', 'JVM', '反射'],
  },
  {
    id: 5,
    title: 'Java Concurrency Fundamentals',
    description: '深入并发编程：从理论基础到字节码实现',
    path: '/chapter5',
    topics: ['并发理论', '设计概念', '块结构并发', 'JMM', '字节码分析'],
  },
  {
    id: 6,
    title: 'JDK Concurrency Libraries',
    description: '从原子操作到异步编程：现代并发工具全景',
    path: '/chapter6',
    topics: ['原子类', 'Lock机制', '并发容器', '阻塞队列', 'Executor框架'],
  },
  {
    id: 7,
    title: 'Understanding Java Performance',
    description: '理解Java性能：从度量到优化的科学方法论，掌握GC、JIT与现代性能分析工具',
    path: '/chapter7',
    topics: ['性能优化', 'GC调优', 'JIT编译', 'JFR/JMC', '性能分析'],
  },
  {
    id: 8,
    title: 'Alternative JVM Languages',
    description: '探索JVM多语言编程：Kotlin、Clojure、Groovy等语言的特性与应用',
    path: '/chapter8',
    topics: ['语言分类法', '多语言编程', '技术选型', 'JVM支持'],
  },
  {
    id: 9,
    title: 'Kotlin',
    description: '现代JVM语言：数据类、空安全、协程与Java互操作',
    path: '/chapter9',
    topics: ['数据类', '空安全', '协程', 'Java互操作'],
  },
  {
    id: 10,
    title: 'Clojure',
    description: '函数式编程精髓：不可变性、REPL驱动开发与序列抽象',
    path: '/chapter10',
    topics: ['不可变性', 'REPL开发', '函数式编程', '序列抽象'],
  },
  {
    id: 11,
    title: 'Building with Gradle and Maven',
    description: '掌控项目构建：深入对比Maven和Gradle的哲学与实践',
    path: '/chapter11',
    topics: ['构建工具', 'Maven', 'Gradle', '依赖管理'],
  },
]

const toggleBookmark = () => {
  isBookmarked.value = !isBookmarked.value
}
</script>

<style scoped>
.book-introduction {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.book-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0;
}

.book-hero-content {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 3rem;
  align-items: center;
}

.book-cover {
  width: 200px;
  height: 280px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.book-cover-placeholder {
  font-size: 4rem;
}

.book-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.book-subtitle {
  font-size: 1.5rem;
  opacity: 0.9;
  margin-bottom: 2rem;
  font-style: italic;
}

.book-meta {
  margin-bottom: 2rem;
}

.meta-item {
  display: flex;
  margin-bottom: 0.5rem;
  gap: 1rem;
}

.meta-label {
  font-weight: 600;
  min-width: 80px;
}

.meta-value {
  opacity: 0.9;
}

.book-actions {
  display: flex;
  gap: 1rem;
}

.start-reading-btn {
  background: white;
  color: #667eea;
  padding: 1rem 2rem;
  border-radius: 30px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.start-reading-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.bookmark-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 1rem 2rem;
  border-radius: 30px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.bookmark-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.book-description,
.chapters-overview,
.learning-features {
  padding: 4rem 0;
}

.book-description {
  background: #f8f9fa;
}

.book-description h2,
.chapters-overview h2,
.learning-features h2 {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 3rem;
  color: #333;
}

.description-content {
  max-width: 800px;
  margin: 0 auto;
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
}

.chapters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.chapter-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.chapter-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.chapter-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.chapter-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2rem;
}

.chapter-title {
  font-size: 1.3rem;
  color: #333;
  margin: 0;
}

.chapter-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.chapter-topics {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.topic-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.chapter-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.chapter-link:hover {
  color: #764ba2;
}

.learning-features {
  background: #f8f9fa;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #333;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .book-hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .book-cover {
    width: 150px;
    height: 210px;
    margin: 0 auto;
  }

  .book-title {
    font-size: 2rem;
  }

  .book-actions {
    flex-direction: column;
    align-items: center;
  }

  .chapters-grid,
  .features-grid {
    grid-template-columns: 1fr;
  }
}
</style>
