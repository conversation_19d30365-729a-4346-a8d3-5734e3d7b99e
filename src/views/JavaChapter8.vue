<template>
  <div class="java-chapter8">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <div class="chapter-info">
            <h1 class="chapter-title">第八章：可选 JVM 语言</h1>
            <p class="chapter-subtitle">Alternative JVM Languages</p>
            <p class="chapter-description">
              探索JVM平台的多语言编程世界：从语言分类法到技术选型，掌握Kotlin、Clojure、Groovy等语言的特性与应用场景
            </p>
          </div>
          <div class="progress-indicator">
            <div class="progress-circle">
              <svg class="progress-ring" width="120" height="120">
                <circle
                  class="progress-ring-circle"
                  stroke="rgba(255,255,255,0.3)"
                  stroke-width="4"
                  fill="transparent"
                  r="52"
                  cx="60"
                  cy="60"
                />
                <circle
                  class="progress-ring-circle progress-ring-circle-fill"
                  stroke="white"
                  stroke-width="4"
                  fill="transparent"
                  r="52"
                  cx="60"
                  cy="60"
                  :stroke-dasharray="`${2 * Math.PI * 52}`"
                  :stroke-dashoffset="`${2 * Math.PI * 52 * (1 - progress / 100)}`"
                />
              </svg>
              <div class="progress-text">{{ Math.round(progress) }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: 语言动物园：编程语言分类法 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="语言动物园：编程语言分类法 (Language Zoology)"
                :concept-data="languageClassificationData"
                @interaction="handleInteraction"
              >
                <div class="language-classification-showcase">
                  <h3>🔍 编程语言的"坐标系"</h3>
                  <p class="intro-text">
                    就像逛动物园时我们给动物分类（食肉、食草、哺乳、爬行），给编程语言分类是为了快速抓住它的"脾气秉性"，知道它擅长干什么。
                  </p>

                  <div class="classification-dimensions">
                    <div class="dimension-card">
                      <h4>📊 编译型 vs. 解释型</h4>
                      <div class="comparison-grid">
                        <div class="comparison-item">
                          <h5>编译型 (Java, Kotlin)</h5>
                          <p>代码在运行前一次性转换成机器码</p>
                          <div class="pros-cons">
                            <div class="pros">
                              <strong>优势：</strong>运行速度快，编译期错误检查
                            </div>
                            <div class="cons"><strong>劣势：</strong>编译时间长，部署复杂</div>
                          </div>
                        </div>
                        <div class="comparison-item">
                          <h5>解释型 (Python, JavaScript)</h5>
                          <p>代码在运行时逐行转换</p>
                          <div class="pros-cons">
                            <div class="pros"><strong>优势：</strong>开发迅速，部署简单</div>
                            <div class="cons"><strong>劣势：</strong>运行较慢，运行时错误</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="dimension-card">
                      <h4>🎯 静态类型 vs. 动态类型</h4>
                      <div class="type-examples">
                        <div class="example-block">
                          <h5>静态类型 (Java)</h5>
                          <div class="code-example">
                            <pre><code>String name = "Ben";
name = 123; // 编译错误！</code></pre>
                          </div>
                          <p>像工厂模具：规格固定，质量保证</p>
                        </div>
                        <div class="example-block">
                          <h5>动态类型 (JavaScript)</h5>
                          <div class="code-example">
                            <pre><code>var name = "Ben";
name = 123; // 完全合法！</code></pre>
                          </div>
                          <p>像橡皮泥：形状可变，创作自由</p>
                        </div>
                      </div>
                    </div>

                    <div class="dimension-card">
                      <h4>⚡ 命令式 vs. 函数式</h4>
                      <div class="paradigm-comparison">
                        <div class="paradigm-item">
                          <h5>命令式编程</h5>
                          <p>核心：修改状态，告诉计算机"怎么做"</p>
                          <div class="code-example">
                            <pre><code>// Java 命令式
List&lt;String&gt; result = new ArrayList&lt;&gt;();
for (String item : list) {
    if (item.length() > 3) {
        result.add(item.toUpperCase());
    }
}</code></pre>
                          </div>
                        </div>
                        <div class="paradigm-item">
                          <h5>函数式编程</h5>
                          <p>核心：转换数据，告诉计算机"要什么"</p>
                          <div class="code-example">
                            <pre><code>// Java 函数式
List&lt;String&gt; result = list.stream()
    .filter(s -&gt; s.length() &gt; 3)
    .map(String::toUpperCase)
    .collect(toList());</code></pre>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="real-world-problems">
                    <h3>🚨 实际项目踩坑与解决方案</h3>
                    <div class="problem-solution">
                      <div class="problem">
                        <h4>常见问题：动态语言的"类型恐慌"</h4>
                        <p>
                          一个Java团队接手Python服务时，频繁遇到TypeError运行时错误。
                          团队抱怨"Python太不安全了"，花费大量时间调试类型问题。
                        </p>
                      </div>
                      <div class="root-cause">
                        <h4>根源分析</h4>
                        <p>
                          问题不在语言本身，而是团队未能转变思维模型。
                          在动态语言中，<strong>完备的单元测试</strong>是第一道防线，而非编译器。
                        </p>
                      </div>
                      <div class="solutions">
                        <h4>业界解决方案</h4>
                        <div class="solution-options">
                          <div class="solution-option">
                            <h5>方案A: 单元测试作为契约</h5>
                            <p>为所有对外接口编写测试，刻意传入错误类型，断言其行为符合预期</p>
                            <div class="trade-offs">
                              <span class="pro">✅ 动态语言的"正道"</span>
                              <span class="con">❌ 测试覆盖率要求高</span>
                            </div>
                          </div>
                          <div class="solution-option">
                            <h5>方案B: 渐进式类型系统</h5>
                            <p>使用Type Hints + MyPy等工具，在提交前静态检查</p>
                            <div class="trade-offs">
                              <span class="pro">✅ 兼顾灵活性与安全性</span>
                              <span class="con">❌ 依赖团队自觉性</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: JVM 上的多语言编程 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="JVM 上的多语言编程 (Polyglot Programming on the JVM)"
                :concept-data="polyglotProgrammingData"
                @interaction="handleInteraction"
              >
                <div class="polyglot-showcase">
                  <h3>🏗️ 多语言编程金字塔</h3>
                  <p class="intro-text">
                    不要试图用一把锤子（Java）去拧所有的螺丝。当你需要一把螺丝刀时，就大胆地去用它。
                    JVM平台让你可以把这些不同的工具放在同一个工具箱里。
                  </p>

                  <div class="pyramid-model">
                    <div class="pyramid-layer stable">
                      <div class="layer-header">
                        <h4>🏛️ 稳定层 (Stable Layer)</h4>
                        <span class="layer-languages">Java, Kotlin, Scala</span>
                      </div>
                      <div class="layer-content">
                        <p><strong>职责：</strong>系统核心，包含核心业务逻辑和数据结构</p>
                        <p><strong>要求：</strong>极其稳定、高性能、类型安全</p>
                        <div class="example">
                          <strong>示例：</strong>电商系统的订单、商品、用户等核心模型和交易处理逻辑
                        </div>
                      </div>
                    </div>

                    <div class="pyramid-layer dynamic">
                      <div class="layer-header">
                        <h4>⚡ 动态层 (Dynamic Layer)</h4>
                        <span class="layer-languages">Clojure, JRuby, Groovy</span>
                      </div>
                      <div class="layer-content">
                        <p>
                          <strong>职责：</strong>围绕稳定层构建，处理Web框架、外部集成、复杂工作流
                        </p>
                        <p><strong>要求：</strong>高开发效率、灵活性、快速迭代</p>
                        <div class="example">
                          <strong>示例：</strong>用Clojure编写管理后台，用Groovy编写CI/CD部署脚本
                        </div>
                      </div>
                    </div>

                    <div class="pyramid-layer domain-specific">
                      <div class="layer-header">
                        <h4>🎯 领域特定层 (Domain-Specific Layer)</h4>
                        <span class="layer-languages">DSL, 规则引擎, 模板引擎</span>
                      </div>
                      <div class="layer-content">
                        <p><strong>职责：</strong>系统最外层，处理高度专业化的问题</p>
                        <p><strong>要求：</strong>极强的表达力，让领域专家也能理解</p>
                        <div class="example">
                          <strong>示例：</strong>用Drools规则引擎定义复杂的促销活动规则
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="analogy-section">
                    <h3>🚢 生动类比：建造航空母舰</h3>
                    <div class="analogy-grid">
                      <div class="analogy-item">
                        <h4>🏗️ 稳定层 = 龙骨、船体、核反应堆</h4>
                        <p>必须使用最坚固、最可靠的材料和工艺，追求极致的稳定与性能</p>
                      </div>
                      <div class="analogy-item">
                        <h4>⚡ 动态层 = 管线、电路、非承重隔断</h4>
                        <p>可以使用更轻便、更易于铺设和修改的材料，追求安装效率</p>
                      </div>
                      <div class="analogy-item">
                        <h4>🎯 领域特定层 = 作战指挥系统界面</h4>
                        <p>高度定制化设计，让指挥官能以最直观的方式理解和操作</p>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 3: 技术选型：如何选择合适的 JVM 语言 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ExpandableSection
                title="技术选型：如何选择合适的 JVM 语言 (Technology Selection)"
                :concept-data="technologySelectionData"
                @interaction="handleInteraction"
              >
                <div class="tech-selection-showcase">
                  <h3>🎯 理性决策框架</h3>
                  <p class="intro-text">
                    在因为一门语言"很酷"就决定用它之前，你需要像一个项目经理一样，
                    冷静地回答一系列"灵魂拷问"，确保这个决定不会在未来把整个团队带进坑里。
                  </p>

                  <div class="evaluation-checklist">
                    <h4>📋 技术选型评估清单</h4>
                    <div class="checklist-grid">
                      <div class="checklist-item">
                        <div class="item-header">
                          <span class="item-icon">⚠️</span>
                          <h5>项目风险</h5>
                        </div>
                        <p>我能在一个低风险、非核心的模块上"试水"吗？</p>
                        <div class="evaluation-criteria">
                          <span class="criteria good">✅ 内部工具、测试代码</span>
                          <span class="criteria bad">❌ 核心业务逻辑</span>
                        </div>
                      </div>

                      <div class="checklist-item">
                        <div class="item-header">
                          <span class="item-icon">🔗</span>
                          <h5>Java互操作性</h5>
                        </div>
                        <p>它能和我现有的Java代码无缝协作吗？</p>
                        <div class="evaluation-criteria">
                          <span class="criteria excellent">🌟 Kotlin: 100%互操作</span>
                          <span class="criteria good">✅ Clojure: 良好支持</span>
                          <span class="criteria warning">⚠️ 小众语言: 需验证</span>
                        </div>
                      </div>

                      <div class="checklist-item">
                        <div class="item-header">
                          <span class="item-icon">🛠️</span>
                          <h5>工具链支持</h5>
                        </div>
                        <p>IDE、构建工具、调试器支持它吗？</p>
                        <div class="evaluation-criteria">
                          <span class="criteria excellent">🌟 IntelliJ IDEA支持</span>
                          <span class="criteria good">✅ Maven/Gradle插件</span>
                          <span class="criteria warning">⚠️ CI/CD集成</span>
                        </div>
                      </div>

                      <div class="checklist-item">
                        <div class="item-header">
                          <span class="item-icon">📈</span>
                          <h5>学习曲线</h5>
                        </div>
                        <p>我的团队需要多久才能熟练掌握它？</p>
                        <div class="evaluation-criteria">
                          <span class="criteria excellent">🌟 Kotlin: 1周上手</span>
                          <span class="criteria good">✅ Groovy: 2-3周</span>
                          <span class="criteria warning">⚠️ Clojure: 1-2个月</span>
                        </div>
                      </div>

                      <div class="checklist-item">
                        <div class="item-header">
                          <span class="item-icon">👥</span>
                          <h5>人才储备</h5>
                        </div>
                        <p>市场上能招到熟悉这门语言的开发者吗？</p>
                        <div class="evaluation-criteria">
                          <span class="criteria excellent">🌟 Kotlin: 快速增长</span>
                          <span class="criteria good">✅ Groovy: 稳定供应</span>
                          <span class="criteria bad">❌ 小众语言: 稀缺</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="case-study">
                    <h3>📊 案例研究：Kotlin引入决策</h3>
                    <div class="case-analysis">
                      <div class="case-scenario">
                        <h4>场景</h4>
                        <p>Java团队想为新开发的内部报表系统引入Kotlin</p>
                      </div>
                      <div class="case-evaluation">
                        <h4>评估结果</h4>
                        <div class="evaluation-results">
                          <div class="result-item pass">
                            <span class="result-icon">✅</span>
                            <span>风险：低（内部新项目）</span>
                          </div>
                          <div class="result-item pass">
                            <span class="result-icon">✅</span>
                            <span>互操作性：极佳（100%兼容）</span>
                          </div>
                          <div class="result-item pass">
                            <span class="result-icon">✅</span>
                            <span>工具链：极佳（IntelliJ顶级支持）</span>
                          </div>
                          <div class="result-item pass">
                            <span class="result-icon">✅</span>
                            <span>学习曲线：低（语法相似）</span>
                          </div>
                          <div class="result-item pass">
                            <span class="result-icon">✅</span>
                            <span>人才储备：快速增长（Android官方语言）</span>
                          </div>
                        </div>
                        <div class="case-conclusion">
                          <strong>结论：</strong>引入Kotlin的决策风险低，收益明确，是明智选择
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="real-world-problems">
                    <h3>🚨 技术选型失败案例</h3>
                    <div class="problem-solution">
                      <div class="problem">
                        <h4>问题：小众函数式语言的"理想主义"陷阱</h4>
                        <p>
                          团队被函数式编程的"优雅"吸引，选择了小众的纯函数式JVM语言重写核心模块。
                          结果陷入：工具链灾难、招聘停滞、认知过载、互操作性问题。
                        </p>
                      </div>
                      <div class="root-cause">
                        <h4>根源分析</h4>
                        <p>
                          典型的"简历驱动开发"和"唯技术论"错误。
                          决策时完全忽略了评估清单中的所有非技术因素。
                        </p>
                      </div>
                      <div class="solutions">
                        <h4>增量式采用与风险控制</h4>
                        <div class="solution-steps">
                          <div class="step">
                            <h5>1. 选择正确的战场</h5>
                            <p>从内部工具、测试代码开始，绝不在核心路径上引入全新语言</p>
                          </div>
                          <div class="step">
                            <h5>2. 验证工具链优先</h5>
                            <p>先搭建完整CI/CD流水线，确保编译、测试、部署等环节顺畅</p>
                          </div>
                          <div class="step">
                            <h5>3. 建立知识分享</h5>
                            <p>通过读书会、Code Review确保知识在团队内流动</p>
                          </div>
                          <div class="step">
                            <h5>4. 逐步扩大根据地</h5>
                            <p>成功后再有计划地、逐步地扩大应用范围</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 4: JVM 的基石：对多语言的支持 -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ExpandableSection
                title="JVM 的基石：对多语言的支持 (The JVM's Foundation for Polyglots)"
                :concept-data="jvmFoundationData"
                @interaction="handleInteraction"
              >
                <div class="jvm-foundation-showcase">
                  <h3>🏗️ JVM多语言支持的两大支柱</h3>
                  <p class="intro-text">
                    JVM就像一个只懂"普通话"（字节码）的强大执行引擎。
                    无论你说"广东话"（Kotlin）还是"上海话"（Clojure），
                    只要带上合格的"翻译"（编译器），JVM就能理解并执行。
                  </p>

                  <div class="foundation-pillars">
                    <div class="pillar">
                      <div class="pillar-header">
                        <span class="pillar-icon">📄</span>
                        <h4>统一的二进制格式：类文件</h4>
                      </div>
                      <div class="pillar-content">
                        <p>
                          所有语言的编译器，无论源语言语法如何，最终都将代码翻译成
                          JVM能理解的、统一的字节码指令，并打包成标准的.class文件格式。
                        </p>
                        <div class="format-flow">
                          <div class="flow-item">
                            <span class="language">Java</span>
                            <span class="arrow">→</span>
                            <span class="compiler">javac</span>
                            <span class="arrow">→</span>
                            <span class="bytecode">.class</span>
                          </div>
                          <div class="flow-item">
                            <span class="language">Kotlin</span>
                            <span class="arrow">→</span>
                            <span class="compiler">kotlinc</span>
                            <span class="arrow">→</span>
                            <span class="bytecode">.class</span>
                          </div>
                          <div class="flow-item">
                            <span class="language">Clojure</span>
                            <span class="arrow">→</span>
                            <span class="compiler">clojure</span>
                            <span class="arrow">→</span>
                            <span class="bytecode">.class</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="pillar">
                      <div class="pillar-header">
                        <span class="pillar-icon">🎭</span>
                        <h4>编译器虚构 (Compiler Fictions)</h4>
                      </div>
                      <div class="pillar-content">
                        <p>
                          源语言中的某些高级特性在JVM层面并不直接存在。
                          编译器会巧妙地将这些特性"翻译"成JVM已有的、更基础的结构。
                        </p>
                        <div class="fiction-examples">
                          <div class="fiction-example">
                            <h5>Java内部类的虚构</h5>
                            <div class="before-after">
                              <div class="before">
                                <h6>源码（你看到的）</h6>
                                <pre><code>class Outer {
    class Inner {
        // 可以访问外部类私有成员
    }
}</code></pre>
                              </div>
                              <div class="after">
                                <h6>字节码（实际生成的）</h6>
                                <pre><code>// Outer.class
class Outer {
    // 合成的桥接方法
    static String access$000(Outer o) {
        return o.privateField;
    }
}

// Outer$Inner.class
class Outer$Inner {
    final Outer this$0; // 外部类引用
}</code></pre>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="analogy-section">
                    <h3>🎬 生动类比：编译器虚构如电影特效</h3>
                    <div class="movie-analogy">
                      <div class="scene">
                        <h4>你看到的：演员在飞</h4>
                        <p>屏幕上，演员轻松地在空中飞行</p>
                      </div>
                      <div class="reality">
                        <h4>实际发生的：钢丝+绿幕</h4>
                        <p>摄影棚里，演员被钢丝吊起，背景是绿幕</p>
                      </div>
                      <div class="compiler-role">
                        <h4>编译器 = 特效团队</h4>
                        <p>为你创造了"飞"的假象，但底层用的是现有技术</p>
                      </div>
                    </div>
                  </div>

                  <div class="real-world-problems">
                    <h3>🚨 跨语言互操作性问题</h3>
                    <div class="problem-solution">
                      <div class="problem">
                        <h4>问题：Clojure库的Java调用困惑</h4>
                        <p>
                          Java团队使用Clojure库时发现： 1. 调用函数需要通过静态方法
                          my_namespace.my_func() 2. 无法直接使用Clojure关键字作为Map的key
                        </p>
                      </div>
                      <div class="root-cause">
                        <h4>根源分析</h4>
                        <p>
                          这是典型的"编译器虚构"和语言间类型系统差异： 1.
                          Clojure函数被虚构成Java静态方法 2. Clojure关键字是特殊对象，与Java
                          String不兼容
                        </p>
                      </div>
                      <div class="solutions">
                        <h4>Facade模式：最佳实践</h4>
                        <div class="facade-pattern">
                          <h5>编写Java适配器</h5>
                          <pre><code>// ClojureLibraryFacade.java
public class ClojureLibraryFacade {
    public static List&lt;String&gt; processData(List&lt;String&gt; input) {
        // 内部处理与Clojure的交互
        // 转换Java String到Clojure Keyword
        // 调用Clojure静态方法
        // 转换返回结果为Java类型
        return result;
    }
}</code></pre>
                          <div class="benefits">
                            <span class="benefit">✅ 清晰的架构边界</span>
                            <span class="benefit">✅ 封装互操作复杂性</span>
                            <span class="benefit">✅ 降低维护成本</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- 章节总结与思维导图 -->
            <section id="topic-4" class="topic-section chapter-summary" ref="topic4">
              <ExpandableSection
                title="📊 章节总结与知识体系图"
                :concept-data="chapterSummaryData"
                @interaction="handleInteraction"
              >
                <div class="summary-content">
                  <h3>🎯 本章核心收获</h3>
                  <div class="key-takeaways">
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🔍</span>
                      <div>
                        <h4>语言分类法</h4>
                        <p>
                          掌握编程语言的核心维度：编译型vs解释型、静态vs动态类型、命令式vs函数式
                        </p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🏗️</span>
                      <div>
                        <h4>多语言编程金字塔</h4>
                        <p>理解稳定层、动态层、领域特定层的分工，为正确场景选择正确工具</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">⚖️</span>
                      <div>
                        <h4>理性技术选型</h4>
                        <p>
                          掌握风险驱动的评估清单：项目风险、互操作性、工具链、学习曲线、人才储备
                        </p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🎭</span>
                      <div>
                        <h4>JVM多语言支持</h4>
                        <p>理解统一字节码格式和编译器虚构，掌握跨语言互操作最佳实践</p>
                      </div>
                    </div>
                  </div>

                  <div class="mindmap-container">
                    <h3>🧠 第八章知识脉络图</h3>
                    <div class="mindmap-wrapper">
                      <div id="chapter8-mindmap" class="mermaid-container">
                        <div class="mindmap-placeholder">
                          <p>🎨 正在生成思维导图...</p>
                          <p>如果长时间未显示，请刷新页面</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="learning-path">
                    <h3>🛤️ 后续学习建议</h3>
                    <div class="path-suggestions">
                      <div class="suggestion">
                        <h4>🚀 实践路径</h4>
                        <ul>
                          <li>选择一个内部工具项目，尝试引入Kotlin</li>
                          <li>学习Groovy，用于编写构建脚本和测试</li>
                          <li>探索Clojure的函数式编程思想</li>
                          <li>研究DSL设计，为特定领域问题创建专用语言</li>
                        </ul>
                      </div>
                      <div class="suggestion">
                        <h4>📚 深入学习</h4>
                        <ul>
                          <li>《Programming Clojure》- 函数式编程深度实践</li>
                          <li>《Kotlin in Action》- Kotlin语言权威指南</li>
                          <li>《Domain-Specific Languages》- DSL设计模式</li>
                          <li>《Seven Languages in Seven Weeks》- 多语言编程视野</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>
          </main>
        </div>
      </div>
    </div>

    <!-- 交互模态框 -->
    <InteractiveModal
      :is-visible="isModalVisible"
      :type="modalData.type"
      :title="modalData.title"
      :data="modalData.data"
      @close="closeModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'
import InteractiveModal from '@/components/InteractiveModal.vue'
import { useInteractiveModal } from '@/composables/useInteractiveModal'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)
const showNotes = ref(false)
const showQuizModal = ref(false)

// 课程主题
const courseTopics = [
  {
    title: '语言动物园：编程语言分类法',
    description: '掌握描述和区分不同编程语言的核心维度',
  },
  {
    title: 'JVM 上的多语言编程',
    description: '理解多语言编程金字塔模型和应用场景',
  },
  {
    title: '技术选型：如何选择合适的 JVM 语言',
    description: '学习基于实践的评估框架和决策清单',
  },
  {
    title: 'JVM 的基石：对多语言的支持',
    description: '探究JVM底层设计如何支撑多种语言运行',
  },
]

// 概念数据
const languageClassificationData = {
  keyPoints: [
    '编程语言分类是技术选型的理论基础',
    '编译型vs解释型：性能与开发效率的权衡',
    '静态vs动态类型：安全性与灵活性的平衡',
    '命令式vs函数式：不同的编程思维模式',
    '现代语言趋向混合范式，Java也在吸收函数式特性',
  ],
  interactiveElements: [
    { type: 'language-comparison', label: '🔍 语言特性对比' },
    { type: 'type-safety-demo', label: '🛡️ 类型安全演示' },
  ],
}

const polyglotProgrammingData = {
  keyPoints: [
    '多语言编程是为正确的问题选择正确的工具',
    '金字塔模型：稳定层、动态层、领域特定层',
    '依赖关系必须是单向的：上层依赖下层',
    '每种语言在其最擅长的领域发挥作用',
    '避免用一种语言"通吃"所有场景',
  ],
  interactiveElements: [
    { type: 'pyramid-demo', label: '🏗️ 金字塔模型演示' },
    { type: 'dependency-flow', label: '📊 依赖关系图' },
  ],
}

const technologySelectionData = {
  keyPoints: [
    '技术选型是投资决策，需要权衡收益与风险',
    '评估清单：项目风险、互操作性、工具链、学习曲线、人才储备',
    '永远从低风险的试点项目开始',
    '验证工具链优先于编写业务代码',
    '增量式采用，避免"毕其功于一役"的幻想',
  ],
  interactiveElements: [
    { type: 'selection-checklist', label: '📋 选型清单工具' },
    { type: 'risk-assessment', label: '⚠️ 风险评估器' },
  ],
}

const jvmFoundationData = {
  keyPoints: [
    'JVM通过统一的字节码格式支持多种语言',
    '编译器虚构：将高级语言特性映射到JVM基础结构',
    '类文件是跨语言、跨平台的通用软件分发格式',
    'Facade模式是处理跨语言互操作的最佳实践',
    '理解底层机制有助于解决复杂的互操作性问题',
  ],
  interactiveElements: [
    { type: 'bytecode-demo', label: '🔍 字节码对比' },
    { type: 'interop-demo', label: '🔗 互操作演示' },
  ],
}

const chapterSummaryData = {
  keyPoints: [
    '掌握JVM多语言编程的理论基础和实践方法',
    '理解语言分类法和多语言编程金字塔模型',
    '学会理性的技术选型和风险控制',
    '了解JVM对多语言支持的底层机制',
  ],
}

// 引用元素
const topic0 = ref<HTMLElement>()
const topic1 = ref<HTMLElement>()
const topic2 = ref<HTMLElement>()
const topic3 = ref<HTMLElement>()
const topic4 = ref<HTMLElement>()

// 方法
const scrollToTopic = (index: number) => {
  const topics = [topic0, topic1, topic2, topic3, topic4]
  const target = topics[index]?.value
  if (target) {
    target.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

const handleScroll = () => {
  const topics = [topic0, topic1, topic2, topic3, topic4]
  const scrollPosition = window.scrollY + 200

  for (let i = topics.length - 1; i >= 0; i--) {
    const element = topics[i]?.value
    if (element && element.offsetTop <= scrollPosition) {
      currentTopic.value = i
      break
    }
  }

  // 更新进度
  const totalHeight = document.documentElement.scrollHeight - window.innerHeight
  const scrolled = window.scrollY
  progress.value = Math.min((scrolled / totalHeight) * 100, 100)
}

const toggleNotes = () => {
  showNotes.value = !showNotes.value
}

const showQuiz = () => {
  showQuizModal.value = true
}

// 使用交互模态框
const { isModalVisible, modalData, handleInteraction, closeModal } = useInteractiveModal()

// 生命周期
onMounted(async () => {
  // 渲染思维导图
  await renderMindMap()

  // 添加滚动监听
  window.addEventListener('scroll', handleScroll, { passive: true })

  // 初始化当前章节
  handleScroll()

  // 模拟进度更新
  const interval = setInterval(() => {
    if (progress.value < 100) {
      progress.value += 2
    } else {
      clearInterval(interval)
    }
  }, 100)
})

onUnmounted(() => {
  // 移除滚动监听
  window.removeEventListener('scroll', handleScroll)
})

const renderMindMap = async () => {
  try {
    console.log('开始初始化 Mermaid...')
    const mermaid = await import('mermaid')
    console.log('Mermaid 模块加载成功:', mermaid)

    mermaid.default.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
    })
    console.log('Mermaid 初始化完成')

    // 延迟渲染以确保DOM已加载
    setTimeout(async () => {
      try {
        console.log('开始渲染 Mermaid 图表...')

        // 创建思维导图内容
        const mindmapContent = `mindmap
  root((第八章 可选JVM语言))
    语言分类法
      编译型vs解释型
      静态类型vs动态类型
      命令式vs函数式
      权衡性能健壮性灵活性
    多语言编程
      多语言金字塔模型
        稳定层Java和Kotlin
        动态层Clojure和Groovy
        领域特定层DSL
      核心思想为正确场景选择正确工具
    技术选型
      实践评估清单
        项目风险
        Java互操作性
        工具链与生态
        学习曲线
        人才储备
      核心思想风险驱动增量采纳
    JVM的支持
      平台中立的字节码
      class文件作为通用二进制格式
      编译器虚构
        内部类转顶级类
        Lambda转invokedynamic
        顶层函数转静态方法
      核心思想源语言特性与JVM实现的解耦`

        const container = document.getElementById('chapter8-mindmap')
        if (container) {
          console.log('找到容器，开始渲染...')
          const { svg } = await mermaid.default.render('chapter8-mindmap-svg', mindmapContent)
          container.innerHTML = svg
          console.log('Mermaid 图表渲染完成')
        } else {
          console.error('未找到思维导图容器')
        }
      } catch (renderError) {
        console.error('Mermaid 渲染错误:', renderError)
        // 渲染失败时显示占位符
        const container = document.getElementById('chapter8-mindmap')
        if (container) {
          container.innerHTML = `
            <div class="mindmap-placeholder">
              <p>🗺️ JVM多语言编程知识体系图</p>
              <p>包含语言分类法、多语言编程、技术选型、JVM支持四大模块</p>
            </div>
          `
        }
      }
    }, 1000)
  } catch (error) {
    console.error('Mermaid 初始化失败:', error)
  }
}
</script>

<style scoped>
/* 基础样式 */
.java-chapter8 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 页面头部样式 */
.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.chapter-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header-content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 3rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.chapter-info h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.chapter-subtitle {
  font-size: 1.5rem;
  opacity: 0.9;
  margin-bottom: 1rem;
  font-style: italic;
}

.chapter-description {
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.95;
  max-width: 600px;
}

/* 进度指示器 */
.progress-indicator {
  position: relative;
}

.progress-circle {
  position: relative;
  width: 120px;
  height: 120px;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle-fill {
  transition: stroke-dashoffset 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.2rem;
  font-weight: 600;
}

/* 内容布局 */
.content-wrapper {
  padding: 2rem 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  align-items: start;
}

/* 侧边栏样式 */
.sidebar {
  position: sticky;
  top: 100px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.outline h3 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin: 0;
  padding: 1.5rem;
  font-size: 1.1rem;
}

.outline-grid {
  padding: 1rem;
}

.outline-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
}

.outline-item:hover {
  background: #f8f9fa;
  transform: translateX(5px);
}

.outline-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.outline-number {
  width: 30px;
  height: 30px;
  background: #e3f2fd;
  color: #1976d2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.outline-item.active .outline-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.outline-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.95rem;
  font-weight: 600;
}

.outline-content p {
  margin: 0;
  font-size: 0.8rem;
  opacity: 0.8;
  line-height: 1.4;
}

/* 工具栏 */
.toolbar {
  padding: 1rem;
  border-top: 1px solid #eee;
  display: flex;
  gap: 0.5rem;
}

.tool-button {
  flex: 1;
  padding: 0.75rem;
  border: none;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.tool-button:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

/* 主内容区 */
.main-content {
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.topic-section {
  margin-bottom: 2rem;
}

.topic-section:last-child {
  margin-bottom: 0;
}

/* 语言分类法样式 */
.language-classification-showcase {
  padding: 2rem;
}

.intro-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.classification-dimensions {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.dimension-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
}

.dimension-card h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.comparison-item {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.comparison-item h5 {
  margin: 0 0 0.5rem 0;
  color: #667eea;
  font-size: 1rem;
}

.comparison-item p {
  margin: 0 0 1rem 0;
  color: #666;
  font-size: 0.9rem;
}

.pros-cons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.pros,
.cons {
  font-size: 0.85rem;
  padding: 0.5rem;
  border-radius: 4px;
}

.pros {
  background: #e8f5e8;
  color: #2e7d32;
}

.cons {
  background: #ffebee;
  color: #c62828;
}

/* 类型示例样式 */
.type-examples {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.example-block {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.example-block h5 {
  margin: 0 0 0.5rem 0;
  color: #667eea;
}

.code-example {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  border-left: 3px solid #667eea;
}

.code-example pre {
  margin: 0;
  white-space: pre-wrap;
}

/* 范式对比样式 */
.paradigm-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.paradigm-item {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.paradigm-item h5 {
  margin: 0 0 0.5rem 0;
  color: #667eea;
}

/* 实际问题解决方案样式 */
.real-world-problems {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #fff3e0;
  border-radius: 12px;
  border: 1px solid #ffcc02;
}

.real-world-problems h3 {
  margin: 0 0 1rem 0;
  color: #e65100;
}

.problem-solution {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.problem,
.root-cause,
.solutions {
  padding: 1rem;
  border-radius: 8px;
}

.problem {
  background: #ffebee;
  border-left: 4px solid #f44336;
}

.root-cause {
  background: #fff3e0;
  border-left: 4px solid #ff9800;
}

.solutions {
  background: #e8f5e8;
  border-left: 4px solid #4caf50;
}

.problem h4,
.root-cause h4,
.solutions h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.solution-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-top: 1rem;
}

.solution-option {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.solution-option h5 {
  margin: 0 0 0.5rem 0;
  color: #4caf50;
}

.trade-offs {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-top: 0.5rem;
}

.pro,
.con {
  font-size: 0.85rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.pro {
  background: #e8f5e8;
  color: #2e7d32;
}

.con {
  background: #ffebee;
  color: #c62828;
}

/* 多语言编程样式 */
.polyglot-showcase {
  padding: 2rem;
}

.pyramid-model {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 2rem 0;
}

.pyramid-layer {
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid;
}

.pyramid-layer.stable {
  background: #e3f2fd;
  border-color: #1976d2;
}

.pyramid-layer.dynamic {
  background: #f3e5f5;
  border-color: #7b1fa2;
}

.pyramid-layer.domain-specific {
  background: #fff3e0;
  border-color: #f57c00;
}

.layer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.layer-header h4 {
  margin: 0;
  font-size: 1.2rem;
}

.layer-languages {
  font-size: 0.9rem;
  font-weight: 600;
  opacity: 0.8;
}

.layer-content p {
  margin: 0.5rem 0;
  line-height: 1.5;
}

.example {
  background: rgba(255, 255, 255, 0.7);
  padding: 0.75rem;
  border-radius: 6px;
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

/* 类比样式 */
.analogy-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.analogy-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.analogy-item {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.analogy-item h4 {
  margin: 0 0 0.5rem 0;
  color: #667eea;
  font-size: 1rem;
}

.analogy-item p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #666;
}

/* 技术选型样式 */
.tech-selection-showcase {
  padding: 2rem;
}

.evaluation-checklist {
  margin: 2rem 0;
}

.evaluation-checklist h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.3rem;
}

.checklist-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.checklist-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
}

.item-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.item-icon {
  font-size: 1.2rem;
}

.item-header h5 {
  margin: 0;
  color: #333;
  font-size: 1rem;
}

.checklist-item p {
  margin: 0 0 1rem 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

.evaluation-criteria {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.criteria {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.criteria.excellent {
  background: #e8f5e8;
  color: #2e7d32;
}

.criteria.good {
  background: #e3f2fd;
  color: #1976d2;
}

.criteria.warning {
  background: #fff3e0;
  color: #f57c00;
}

.criteria.bad {
  background: #ffebee;
  color: #c62828;
}

/* 案例研究样式 */
.case-study {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #e8f5e8;
  border-radius: 12px;
  border: 1px solid #4caf50;
}

.case-study h3 {
  margin: 0 0 1rem 0;
  color: #2e7d32;
}

.case-analysis {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 1rem;
}

.case-scenario {
  background: white;
  padding: 1rem;
  border-radius: 8px;
}

.case-scenario h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.case-evaluation {
  background: white;
  padding: 1rem;
  border-radius: 8px;
}

.case-evaluation h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.evaluation-results {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 6px;
}

.result-item.pass {
  background: #e8f5e8;
  color: #2e7d32;
}

.result-icon {
  font-size: 1rem;
}

.case-conclusion {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #4caf50;
}

/* 解决方案步骤样式 */
.solution-steps {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.step {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #4caf50;
}

.step h5 {
  margin: 0 0 0.5rem 0;
  color: #2e7d32;
}

.step p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

/* JVM基础样式 */
.jvm-foundation-showcase {
  padding: 2rem;
}

.foundation-pillars {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin: 2rem 0;
}

.pillar {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
}

.pillar-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.pillar-icon {
  font-size: 1.5rem;
}

.pillar-header h4 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.pillar-content p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
  color: #555;
}

/* 格式流程样式 */
.format-flow {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
}

.flow-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.language {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-weight: 600;
  font-size: 0.85rem;
}

.compiler {
  background: #f3e5f5;
  color: #7b1fa2;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-weight: 600;
  font-size: 0.85rem;
}

.bytecode {
  background: #fff3e0;
  color: #f57c00;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-weight: 600;
  font-size: 0.85rem;
}

.arrow {
  color: #666;
  font-weight: bold;
}

/* 虚构示例样式 */
.fiction-examples {
  margin-top: 1rem;
}

.fiction-example {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.fiction-example h5 {
  margin: 0 0 1rem 0;
  color: #667eea;
}

.before-after {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.before,
.after {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
}

.before h6,
.after h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 0.9rem;
}

.before pre,
.after pre {
  margin: 0;
  font-size: 0.8rem;
  line-height: 1.4;
  white-space: pre-wrap;
}

/* 电影类比样式 */
.movie-analogy {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.scene,
.reality,
.compiler-role {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  text-align: center;
}

.scene h4,
.reality h4,
.compiler-role h4 {
  margin: 0 0 0.5rem 0;
  color: #667eea;
  font-size: 1rem;
}

.scene p,
.reality p,
.compiler-role p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

/* Facade模式样式 */
.facade-pattern {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.facade-pattern h5 {
  margin: 0 0 0.5rem 0;
  color: #4caf50;
}

.facade-pattern pre {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  margin: 0.5rem 0;
  font-size: 0.8rem;
  line-height: 1.4;
  overflow-x: auto;
}

.benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.benefit {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* 章节总结样式 */
.chapter-summary {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 15px;
  margin-top: 2rem;
}

.summary-content {
  padding: 2rem;
}

.summary-content h3 {
  margin: 0 0 2rem 0;
  color: #333;
  font-size: 1.5rem;
  text-align: center;
}

.key-takeaways {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.takeaway-item {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 1rem;
  align-items: flex-start;
  transition: all 0.3s ease;
}

.takeaway-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.takeaway-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.takeaway-item h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.takeaway-item p {
  margin: 0;
  color: #666;
  line-height: 1.5;
  font-size: 0.95rem;
}

/* 思维导图样式 */
.mindmap-container {
  margin: 2rem 0;
  text-align: center;
}

.mindmap-container h3 {
  margin: 0 0 1rem 0;
  color: #333;
}

.mindmap-wrapper {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.mermaid-container {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mindmap-placeholder {
  text-align: center;
  color: #666;
  padding: 2rem;
}

.mindmap-placeholder p {
  margin: 0.5rem 0;
}

/* 学习路径样式 */
.learning-path {
  margin-top: 2rem;
}

.learning-path h3 {
  margin: 0 0 1rem 0;
  color: #333;
  text-align: center;
}

.path-suggestions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.suggestion {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.suggestion h4 {
  margin: 0 0 1rem 0;
  color: #667eea;
  font-size: 1.1rem;
}

.suggestion ul {
  margin: 0;
  padding-left: 1.5rem;
}

.suggestion li {
  margin-bottom: 0.5rem;
  color: #666;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .sidebar {
    position: static;
    order: -1;
  }

  .outline-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
  }

  .outline-item {
    margin-bottom: 0;
  }
}

@media (max-width: 768px) {
  .header-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .chapter-info h1 {
    font-size: 2rem;
  }

  .container {
    padding: 0 1rem;
  }

  .comparison-grid,
  .type-examples,
  .paradigm-comparison,
  .solution-options,
  .before-after,
  .case-analysis,
  .path-suggestions {
    grid-template-columns: 1fr;
  }

  .checklist-grid {
    grid-template-columns: 1fr;
  }

  .key-takeaways {
    grid-template-columns: 1fr;
  }

  .movie-analogy {
    grid-template-columns: 1fr;
  }

  .analogy-grid {
    grid-template-columns: 1fr;
  }

  .pyramid-model {
    gap: 1rem;
  }

  .layer-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .format-flow {
    gap: 0.25rem;
  }

  .flow-item {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .chapter-info h1 {
    font-size: 1.5rem;
  }

  .chapter-subtitle {
    font-size: 1.2rem;
  }

  .progress-circle {
    width: 80px;
    height: 80px;
  }

  .progress-text {
    font-size: 1rem;
  }

  .language-classification-showcase,
  .polyglot-showcase,
  .tech-selection-showcase,
  .jvm-foundation-showcase {
    padding: 1rem;
  }

  .summary-content {
    padding: 1rem;
  }

  .mindmap-wrapper {
    padding: 1rem;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.topic-section {
  animation: fadeInUp 0.6s ease-out;
}

.takeaway-item {
  animation: fadeInUp 0.6s ease-out;
}

.takeaway-item:nth-child(2) {
  animation-delay: 0.1s;
}

.takeaway-item:nth-child(3) {
  animation-delay: 0.2s;
}

.takeaway-item:nth-child(4) {
  animation-delay: 0.3s;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
</style>
