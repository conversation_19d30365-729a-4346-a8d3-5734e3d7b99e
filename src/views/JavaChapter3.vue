<template>
  <div class="java-chapter3">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <h1 class="chapter-title">第三章：Java 17 现代特性</h1>
          <p class="chapter-subtitle">探索文本块、Switch表达式、Records、密封类型与模式匹配</p>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
            <span class="progress-text">{{ progress }}% 完成</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: 文本块 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="文本块 (Text Blocks)"
                :concept-data="textBlocksData"
                @interaction="handleInteraction"
              >
                <div class="text-blocks-showcase">
                  <h3>📝 文本块：告别字符串拼接地狱</h3>

                  <div class="feature-intro">
                    <div class="intro-card">
                      <div class="intro-header">
                        <span class="intro-icon">🎯</span>
                        <h4>解决的痛点</h4>
                      </div>
                      <ul class="pain-points">
                        <li>多行字符串需要大量 <code>\n</code> 和 <code>+</code></li>
                        <li>嵌入SQL、JSON、HTML时代码丑陋难维护</li>
                        <li>引号转义让代码可读性极差</li>
                        <li>缩进和格式化困难</li>
                      </ul>
                    </div>

                    <div class="intro-card">
                      <div class="intro-header">
                        <span class="intro-icon">✨</span>
                        <h4>文本块优势</h4>
                      </div>
                      <ul class="advantages">
                        <li>三引号 <code>"""</code> 包裹，所见即所得</li>
                        <li>智能缩进处理，自动去除公共前导空格</li>
                        <li>自然的换行符处理</li>
                        <li>极大提升代码可读性</li>
                      </ul>
                    </div>
                  </div>

                  <div class="syntax-demo">
                    <h4>语法演示</h4>
                    <div class="demo-tabs">
                      <button
                        v-for="(demo, index) in textBlockDemos"
                        :key="index"
                        @click="selectedTextDemo = index"
                        :class="['tab-btn', { active: selectedTextDemo === index }]"
                      >
                        {{ demo.title }}
                      </button>
                    </div>
                    <div class="demo-content">
                      <div class="code-comparison">
                        <div class="before-after">
                          <div class="before">
                            <h5>传统方式 😰</h5>
                            <pre class="code-block old">{{
                              textBlockDemos[selectedTextDemo].before
                            }}</pre>
                          </div>
                          <div class="after">
                            <h5>文本块方式 😍</h5>
                            <pre class="code-block new">{{
                              textBlockDemos[selectedTextDemo].after
                            }}</pre>
                          </div>
                        </div>
                        <div class="explanation">
                          <h5>💡 关键改进</h5>
                          <p>{{ textBlockDemos[selectedTextDemo].explanation }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- F. 项目实践踩坑与解决方案 -->
                <div class="real-world-problems">
                  <h3>🔧 项目实践踩坑与解决方案</h3>

                  <div class="problem-section">
                    <h4>💥 常见问题描述</h4>
                    <div class="problem-cards">
                      <div class="problem-card critical">
                        <div class="problem-header">
                          <span class="problem-icon">📝</span>
                          <h5>文本块缩进处理导致的格式混乱</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >在使用文本块处理SQL、JSON、XML等格式化文本时，由于对缩进规则理解不当，导致输出的文本格式与预期不符，影响数据解析或显示效果。
                          </p>
                          <p>
                            <strong>表现：</strong
                            >生成的SQL语句缩进错乱，JSON格式不正确，或者HTML模板渲染异常，调试时发现文本内容包含意外的空格或缺少必要的缩进。
                          </p>
                        </div>
                      </div>

                      <div class="problem-card warning">
                        <div class="problem-header">
                          <span class="problem-icon">🔄</span>
                          <h5>文本块与传统字符串混用导致的兼容性问题</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >在大型项目中部分使用文本块，部分仍使用传统字符串拼接，导致代码风格不一致，维护困难，且在字符串处理逻辑上出现不一致的行为。
                          </p>
                          <p>
                            <strong>表现：</strong
                            >同一个功能模块中存在多种字符串处理方式，代码审查困难，新团队成员不知道何时使用哪种方式。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="root-cause-analysis">
                    <h4>🔍 问题根源分析</h4>
                    <div class="analysis-content">
                      <div class="cause-item">
                        <span class="cause-icon">📏</span>
                        <div>
                          <h5>缩进算法的复杂性</h5>
                          <p>
                            文本块的缩进处理基于"公共前导空格"算法，开发者往往不理解这个算法的具体规则，特别是在混合使用空格和制表符时，容易产生意外的结果。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">🔧</span>
                        <div>
                          <h5>IDE支持的不一致性</h5>
                          <p>
                            不同IDE对文本块的格式化和显示支持程度不同，有些IDE可能不能正确显示文本块的实际输出效果，导致开发者对结果的误判。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">📚</span>
                        <div>
                          <h5>团队规范缺失</h5>
                          <p>
                            缺乏明确的文本块使用规范和最佳实践指导，团队成员各自按照理解使用，导致代码风格不统一和潜在的维护问题。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="solutions-section">
                    <h4>💡 业界主流解决方案与权衡</h4>
                    <div class="solutions-grid">
                      <div class="solution-card recommended">
                        <div class="solution-header">
                          <span class="solution-icon">📋</span>
                          <h5>制定文本块使用规范与工具支持</h5>
                          <span class="solution-badge best">推荐方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >建立团队级别的文本块使用规范，包括适用场景、缩进规则、格式化标准，并配置IDE插件和静态分析工具进行自动检查。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>确保团队代码风格一致性</li>
                                <li>减少格式化相关的bug</li>
                                <li>提高代码可读性和维护性</li>
                                <li>便于新成员快速上手</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>需要投入时间制定和维护规范</li>
                                <li>可能限制某些灵活的使用方式</li>
                                <li>需要团队达成共识</li>
                                <li>工具配置和维护成本</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong>适合所有使用Java
                              17+的团队，前期投入换取长期的开发效率提升和代码质量保障。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card alternative">
                        <div class="solution-header">
                          <span class="solution-icon">🔄</span>
                          <h5>渐进式迁移与混合策略</h5>
                          <span class="solution-badge stable">过渡方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >在新功能中优先使用文本块，对现有代码进行渐进式重构，建立迁移指南和兼容性检查，确保新旧代码能够和谐共存。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>风险可控，不影响现有功能</li>
                                <li>可以逐步积累使用经验</li>
                                <li>便于团队学习和适应</li>
                                <li>减少大规模重构的风险</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>代码风格不统一的时间较长</li>
                                <li>需要维护两套字符串处理逻辑</li>
                                <li>可能出现混合使用的复杂情况</li>
                                <li>迁移周期较长</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合大型遗留项目或对稳定性要求极高的系统，能够平衡新特性采用和系统稳定性。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card temporary">
                        <div class="solution-header">
                          <span class="solution-icon">🛡️</span>
                          <h5>保守的传统字符串策略</h5>
                          <span class="solution-badge conservative">保守方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >暂时不使用文本块特性，继续使用传统的字符串拼接和StringBuilder，等待特性成熟和团队技能提升后再考虑迁移。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>无需学习新语法，风险最低</li>
                                <li>与现有代码完全兼容</li>
                                <li>团队熟悉现有方式</li>
                                <li>避免潜在的格式化问题</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>无法享受文本块的便利性</li>
                                <li>多行字符串仍然难以维护</li>
                                <li>错过语言现代化的机会</li>
                                <li>代码可读性提升有限</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合对新特性采用非常谨慎的团队或关键业务系统，但会错过显著的开发体验改善。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: Switch表达式 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="Switch 表达式 (Switch Expressions)"
                :concept-data="switchExpressionsData"
                @interaction="handleInteraction"
              >
                <div class="switch-expressions-showcase">
                  <h3>🔀 Switch表达式：从语句到表达式的华丽转身</h3>

                  <div class="evolution-timeline">
                    <div class="timeline-item">
                      <div class="timeline-marker old">旧</div>
                      <div class="timeline-content">
                        <h4>传统Switch语句</h4>
                        <ul>
                          <li>只能执行动作，不能返回值</li>
                          <li>需要大量break语句</li>
                          <li>case穿透容易出错</li>
                          <li>样板代码冗长</li>
                        </ul>
                      </div>
                    </div>
                    <div class="timeline-arrow">→</div>
                    <div class="timeline-item">
                      <div class="timeline-marker new">新</div>
                      <div class="timeline-content">
                        <h4>Switch表达式</h4>
                        <ul>
                          <li>可以返回值，支持赋值</li>
                          <li>箭头语法，无需break</li>
                          <li>编译时详尽性检查</li>
                          <li>代码简洁安全</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div class="syntax-features">
                    <h4>🚀 新语法特性</h4>
                    <div class="features-grid">
                      <div class="feature-card">
                        <div class="feature-icon">➡️</div>
                        <h5>箭头语法</h5>
                        <code>case L -> expression</code>
                        <p>自动跳出，无case穿透</p>
                      </div>
                      <div class="feature-card">
                        <div class="feature-icon">🔢</div>
                        <h5>多值匹配</h5>
                        <code>case 1, 2, 3 -> ...</code>
                        <p>一个case处理多个值</p>
                      </div>
                      <div class="feature-card">
                        <div class="feature-icon">📤</div>
                        <h5>yield关键字</h5>
                        <code>yield value;</code>
                        <p>在代码块中返回值</p>
                      </div>
                      <div class="feature-card">
                        <div class="feature-icon">✅</div>
                        <h5>详尽性检查</h5>
                        <code>编译器强制覆盖所有情况</code>
                        <p>确保表达式总有返回值</p>
                      </div>
                    </div>
                  </div>

                  <div class="code-examples">
                    <h4>💻 详细代码示例</h4>
                    <div class="examples-tabs">
                      <button
                        v-for="(example, index) in switchExamples"
                        :key="index"
                        @click="selectedSwitchExample = index"
                        :class="['example-tab', { active: selectedSwitchExample === index }]"
                      >
                        {{ example.title }}
                      </button>
                    </div>
                    <div class="example-content">
                      <div class="code-comparison">
                        <div class="traditional-code">
                          <h5>传统Switch语句 😰</h5>
                          <pre class="code-block old">{{
                            switchExamples[selectedSwitchExample].traditional
                          }}</pre>
                          <div class="code-problems">
                            <span
                              v-for="problem in switchExamples[selectedSwitchExample].problems"
                              :key="problem"
                              class="problem-tag"
                              >❌ {{ problem }}</span
                            >
                          </div>
                        </div>
                        <div class="modern-code">
                          <h5>Switch表达式 😍</h5>
                          <pre class="code-block new">{{
                            switchExamples[selectedSwitchExample].modern
                          }}</pre>
                          <div class="code-benefits">
                            <span
                              v-for="benefit in switchExamples[selectedSwitchExample].benefits"
                              :key="benefit"
                              class="benefit-tag"
                              >✅ {{ benefit }}</span
                            >
                          </div>
                        </div>
                      </div>
                      <div class="example-explanation">
                        <h5>🔍 关键改进点</h5>
                        <p>{{ switchExamples[selectedSwitchExample].explanation }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- F. 项目实践踩坑与解决方案 -->
                <div class="real-world-problems">
                  <h3>🔧 项目实践踩坑与解决方案</h3>

                  <div class="problem-section">
                    <h4>💥 常见问题描述</h4>
                    <div class="problem-cards">
                      <div class="problem-card critical">
                        <div class="problem-header">
                          <span class="problem-icon">🔀</span>
                          <h5>Switch表达式详尽性检查导致的编译错误</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >在使用Switch表达式处理枚举或密封类型时，编译器要求覆盖所有可能的情况，但开发者遗漏了某些case或者在枚举新增值后忘记更新Switch表达式，导致编译失败。
                          </p>
                          <p>
                            <strong>表现：</strong>编译器报错"the switch expression does not cover
                            all possible input
                            values"，特别是在处理复杂的业务状态枚举时，容易遗漏边界情况。
                          </p>
                        </div>
                      </div>

                      <div class="problem-card warning">
                        <div class="problem-header">
                          <span class="problem-icon">🔄</span>
                          <h5>传统Switch与Switch表达式混用导致的代码不一致</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >在大型项目中，部分代码使用了新的Switch表达式，部分仍使用传统Switch语句，导致代码风格不统一，维护困难，且容易在重构时引入错误。
                          </p>
                          <p>
                            <strong>表现：</strong
                            >同一个类中存在两种Switch写法，代码审查时需要额外关注语法差异，新手容易混淆两种语法的使用场景。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="root-cause-analysis">
                    <h4>🔍 问题根源分析</h4>
                    <div class="analysis-content">
                      <div class="cause-item">
                        <span class="cause-icon">🔍</span>
                        <div>
                          <h5>详尽性检查的严格性</h5>
                          <p>
                            Switch表达式要求在编译时就能确定所有可能的返回路径，这种严格性虽然提高了代码安全性，但也增加了开发时的约束，特别是在处理开放式枚举或未来可能扩展的类型时。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">🔄</span>
                        <div>
                          <h5>语法迁移的渐进性</h5>
                          <p>
                            Switch表达式是相对较新的特性，团队在迁移过程中往往采用渐进式策略，导致新旧语法并存，增加了代码复杂度和维护成本。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">📚</span>
                        <div>
                          <h5>最佳实践的缺乏</h5>
                          <p>
                            由于Switch表达式是较新的特性，业界还没有形成完整的最佳实践指南，开发者在使用时容易踩坑，特别是在复杂业务逻辑的处理上。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="solutions-section">
                    <h4>💡 业界主流解决方案与权衡</h4>
                    <div class="solutions-grid">
                      <div class="solution-card recommended">
                        <div class="solution-header">
                          <span class="solution-icon">🎯</span>
                          <h5>基于场景的Switch使用策略</h5>
                          <span class="solution-badge best">推荐方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >制定明确的Switch使用指南：表达式用于简单的值映射和计算，语句用于复杂的业务逻辑处理，建立代码审查检查点确保一致性。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>充分发挥两种语法的优势</li>
                                <li>代码意图清晰，易于理解</li>
                                <li>减少语法选择的困惑</li>
                                <li>便于团队协作和维护</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>需要团队学习两套语法</li>
                                <li>场景划分可能存在争议</li>
                                <li>需要额外的代码审查工作</li>
                                <li>规范制定和维护成本</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合技术能力较强的团队，能够最大化利用Java现代特性的优势，提升代码质量和开发效率。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card alternative">
                        <div class="solution-header">
                          <span class="solution-icon">🔧</span>
                          <h5>工具辅助的自动化迁移</h5>
                          <span class="solution-badge stable">技术方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >使用IDE重构工具和静态分析工具，自动识别适合转换为Switch表达式的传统Switch语句，并提供自动转换功能，减少手动迁移的工作量和错误。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>自动化程度高，减少人工错误</li>
                                <li>可以快速处理大量代码</li>
                                <li>保证转换的正确性</li>
                                <li>节省开发时间</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>工具可能无法处理复杂情况</li>
                                <li>需要人工验证转换结果</li>
                                <li>可能产生不够优雅的代码</li>
                                <li>依赖工具的成熟度</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合有大量遗留代码需要迁移的项目，能够显著提升迁移效率，但需要配合人工审查。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card temporary">
                        <div class="solution-header">
                          <span class="solution-icon">🛡️</span>
                          <h5>保守的传统Switch策略</h5>
                          <span class="solution-badge conservative">保守方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >暂时继续使用传统Switch语句，避免引入新的语法复杂性，等待团队技能提升和工具链成熟后再考虑迁移到Switch表达式。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>团队熟悉，学习成本为零</li>
                                <li>避免详尽性检查的约束</li>
                                <li>与现有代码完全兼容</li>
                                <li>减少潜在的语法错误</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>无法享受表达式的简洁性</li>
                                <li>仍然存在case穿透的风险</li>
                                <li>错过编译时安全检查</li>
                                <li>代码冗长度较高</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合对新特性采用非常谨慎的团队，能够保持代码稳定性但会错过语言现代化的收益。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 3: 记录类 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ExpandableSection
                title="记录类 (Records)"
                :concept-data="recordsData"
                @interaction="handleInteraction"
              >
                <div class="records-showcase">
                  <h3>📋 记录类：数据类的终极简化</h3>

                  <div class="records-concept">
                    <div class="concept-card">
                      <div class="concept-header">
                        <span class="concept-icon">🎯</span>
                        <h4>设计理念</h4>
                      </div>
                      <div class="concept-content">
                        <p>
                          <strong>"类即其状态"</strong> -
                          当一个类的全部意义就在于它所持有的数据时，为什么要写几十行样板代码？
                        </p>
                        <div class="philosophy">
                          <div class="phil-item">
                            <span class="phil-icon">🏗️</span>
                            <span>自动生成构造器、访问器、equals、hashCode、toString</span>
                          </div>
                          <div class="phil-item">
                            <span class="phil-icon">🔒</span>
                            <span>天然不可变性，所有字段都是final</span>
                          </div>
                          <div class="phil-item">
                            <span class="phil-icon">🎨</span>
                            <span>极简语法，一行代码定义完整数据类</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="records-comparison">
                    <h4>📊 代码量对比</h4>
                    <div class="comparison-stats">
                      <div class="stat-card traditional">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">传统POJO行数</div>
                        <div class="stat-details">
                          <span>构造器</span>
                          <span>Getter方法</span>
                          <span>equals/hashCode</span>
                          <span>toString</span>
                        </div>
                      </div>
                      <div class="vs-arrow">VS</div>
                      <div class="stat-card record">
                        <div class="stat-number">1</div>
                        <div class="stat-label">Record声明行数</div>
                        <div class="stat-details">
                          <span>自动生成一切</span>
                          <span>编译时保证</span>
                          <span>类型安全</span>
                          <span>不可变性</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="records-features">
                    <h4>🔧 高级特性</h4>
                    <div class="advanced-features">
                      <div class="feature-item">
                        <div class="feature-title">
                          <span class="feature-emoji">🛡️</span>
                          <span>紧凑构造器</span>
                        </div>
                        <div class="feature-desc">
                          <p>用于参数验证和规范化，无需显式参数列表</p>
                          <pre class="mini-code">
public Person {
    if (age < 0) throw new IllegalArgumentException();
}</pre
                          >
                        </div>
                      </div>
                      <div class="feature-item">
                        <div class="feature-title">
                          <span class="feature-emoji">🔗</span>
                          <span>与密封类型协同</span>
                        </div>
                        <div class="feature-desc">
                          <p>Records天然是final的，是sealed类型的完美子类</p>
                          <pre class="mini-code">
sealed interface Shape permits Circle, Rectangle {}
record Circle(double radius) implements Shape {}</pre
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- F. 项目实践踩坑与解决方案 -->
                <div class="real-world-problems">
                  <h3>🔧 项目实践踩坑与解决方案</h3>

                  <div class="problem-section">
                    <h4>💥 常见问题描述</h4>
                    <div class="problem-cards">
                      <div class="problem-card critical">
                        <div class="problem-header">
                          <span class="problem-icon">📋</span>
                          <h5>Records不可变性导致的业务逻辑复杂化</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >在需要频繁修改对象状态的业务场景中使用Records，由于其不可变性，每次修改都需要创建新实例，导致代码冗长，性能下降，且容易出现状态不一致的问题。
                          </p>
                          <p>
                            <strong>表现：</strong
                            >大量的"with"方法或构造器调用，内存占用增加，垃圾回收压力大，代码可读性下降。
                          </p>
                        </div>
                      </div>

                      <div class="problem-card warning">
                        <div class="problem-header">
                          <span class="problem-icon">🔧</span>
                          <h5>Records与现有框架集成的兼容性问题</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >在使用Records与Spring、Hibernate、Jackson等框架集成时，遇到序列化/反序列化问题、JPA实体映射失败、或者依赖注入不工作等兼容性问题。
                          </p>
                          <p>
                            <strong>表现：</strong
                            >JSON序列化格式异常、数据库映射错误、或者框架无法识别Records的构造器和访问器方法。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="solutions-section">
                    <h4>💡 业界主流解决方案与权衡</h4>
                    <div class="solutions-grid">
                      <div class="solution-card recommended">
                        <div class="solution-header">
                          <span class="solution-icon">🎯</span>
                          <h5>场景驱动的Records使用策略</h5>
                          <span class="solution-badge best">推荐方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >明确Records的适用场景：数据传输对象(DTO)、值对象、配置类等，对于需要频繁修改的业务实体继续使用传统类，建立清晰的使用指南。
                          </p>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合大多数企业项目，能够在简洁性和实用性之间取得平衡，最大化Records的收益。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card alternative">
                        <div class="solution-header">
                          <span class="solution-icon">🔧</span>
                          <h5>框架适配与工具增强</h5>
                          <span class="solution-badge stable">技术方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >使用框架的Records支持特性，配置相应的注解和适配器，或者使用第三方库来增强Records的功能，如Lombok的@With注解替代方案。
                          </p>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合需要与现有框架深度集成的项目，能够保持Records的优势同时解决兼容性问题。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 4: 密封类型 -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ExpandableSection
                title="密封类型 (Sealed Types)"
                :concept-data="sealedTypesData"
                @interaction="handleInteraction"
              >
                <div class="sealed-types-showcase">
                  <h3>🔒 密封类型：精确控制继承体系</h3>

                  <div class="inheritance-control">
                    <h4>🎯 继承控制的演进</h4>
                    <div class="control-spectrum">
                      <div class="control-option old">
                        <div class="control-header">
                          <span class="control-icon">🔓</span>
                          <span>public class</span>
                        </div>
                        <div class="control-desc">
                          <p>任何人都可以继承</p>
                          <span class="control-problem">❌ 无法控制子类</span>
                        </div>
                      </div>

                      <div class="control-option old">
                        <div class="control-header">
                          <span class="control-icon">🔒</span>
                          <span>final class</span>
                        </div>
                        <div class="control-desc">
                          <p>完全禁止继承</p>
                          <span class="control-problem">❌ 过于严格</span>
                        </div>
                      </div>

                      <div class="control-option new">
                        <div class="control-header">
                          <span class="control-icon">🎯</span>
                          <span>sealed class</span>
                        </div>
                        <div class="control-desc">
                          <p>精确控制谁可以继承</p>
                          <span class="control-benefit">✅ 完美平衡</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="sealed-rules">
                    <h4>📋 密封类型规则</h4>
                    <div class="rules-grid">
                      <div class="rule-card">
                        <div class="rule-number">1</div>
                        <div class="rule-content">
                          <h5>permits声明</h5>
                          <p>必须明确列出所有允许的直接子类</p>
                          <code>sealed class Pet permits Cat, Dog</code>
                        </div>
                      </div>
                      <div class="rule-card">
                        <div class="rule-number">2</div>
                        <div class="rule-content">
                          <h5>子类义务</h5>
                          <p>每个子类必须选择：final、sealed或non-sealed</p>
                          <code>final class Cat extends Pet</code>
                        </div>
                      </div>
                      <div class="rule-card">
                        <div class="rule-number">3</div>
                        <div class="rule-content">
                          <h5>位置限制</h5>
                          <p>子类必须在同一模块或包中</p>
                          <code>// 确保可见性和控制</code>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 5: 模式匹配 -->
            <section id="topic-4" class="topic-section" ref="topic4">
              <ExpandableSection
                title="模式匹配与预览特性"
                :concept-data="patternMatchingData"
                @interaction="handleInteraction"
              >
                <div class="pattern-matching-showcase">
                  <h3>🎯 模式匹配：类型检查与数据提取的优雅结合</h3>

                  <div class="pattern-evolution">
                    <h4>🚀 模式匹配的演进历程</h4>
                    <div class="evolution-steps">
                      <div class="step-card current">
                        <div class="step-header">
                          <span class="step-badge">Java 16</span>
                          <h5>instanceof 模式匹配</h5>
                        </div>
                        <div class="step-content">
                          <pre class="step-code">
if (obj instanceof String s) {
    System.out.println(s.toUpperCase());
}</pre
                          >
                          <p>消除了类型检查后的强制转换</p>
                        </div>
                      </div>

                      <div class="step-card preview">
                        <div class="step-header">
                          <span class="step-badge preview">Java 17 预览</span>
                          <h5>Switch 模式匹配</h5>
                        </div>
                        <div class="step-content">
                          <pre class="step-code">
return switch (obj) {
    case String s -> s.length();
    case Integer i -> i * 2;
    case null -> 0;
    default -> -1;
};</pre
                          >
                          <p>将模式匹配扩展到switch表达式</p>
                        </div>
                      </div>

                      <div class="step-card future">
                        <div class="step-header">
                          <span class="step-badge future">未来</span>
                          <h5>解构模式</h5>
                        </div>
                        <div class="step-content">
                          <pre class="step-code">
switch (shape) {
    case Circle(var radius) -> ...;
    case Rectangle(var w, var h) -> ...;
}</pre
                          >
                          <p>直接解构Record和其他数据结构</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="pattern-benefits">
                    <h4>💎 模式匹配的核心价值</h4>
                    <div class="benefits-showcase">
                      <div class="benefit-item">
                        <div class="benefit-icon">🛡️</div>
                        <div class="benefit-content">
                          <h5>类型安全</h5>
                          <p>编译时保证类型正确性，消除ClassCastException风险</p>
                        </div>
                      </div>
                      <div class="benefit-item">
                        <div class="benefit-icon">✂️</div>
                        <div class="benefit-content">
                          <h5>减少样板代码</h5>
                          <p>一行代码完成类型检查、转换和变量绑定</p>
                        </div>
                      </div>
                      <div class="benefit-item">
                        <div class="benefit-icon">🔗</div>
                        <div class="benefit-content">
                          <h5>与其他特性协同</h5>
                          <p>与Records、Sealed Types完美配合，构建强大的数据处理能力</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Java 17 特性演示 -->
            <section class="topic-section">
              <ExpandableSection
                title="🚀 Java 17 特性互动演示"
                :concept-data="{ keyPoints: ['体验所有新特性的实际应用'] }"
                @interaction="handleInteraction"
              >
                <Java17FeaturesDemo />
              </ExpandableSection>
            </section>

            <!-- 章节总结与思维导图 -->
            <section id="topic-5" class="topic-section chapter-summary" ref="topic5">
              <ExpandableSection
                title="📊 章节总结与知识体系图"
                :concept-data="chapterSummaryData"
                @interaction="handleInteraction"
              >
                <div class="summary-content">
                  <h3>🎯 本章核心收获</h3>
                  <div class="key-takeaways">
                    <div class="takeaway-item">
                      <span class="takeaway-icon">📝</span>
                      <div>
                        <h4>文本块革命</h4>
                        <p>三引号语法彻底解决多行字符串的可读性问题</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🔀</span>
                      <div>
                        <h4>Switch表达式</h4>
                        <p>从语句到表达式，支持返回值和详尽性检查</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">📋</span>
                      <div>
                        <h4>Records数据类</h4>
                        <p>一行代码定义完整数据类，自动生成所有样板代码</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🔒</span>
                      <div>
                        <h4>密封类型</h4>
                        <p>精确控制继承体系，构建安全的类型层次</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🎯</span>
                      <div>
                        <h4>模式匹配</h4>
                        <p>类型安全的数据提取，向函数式编程迈进</p>
                      </div>
                    </div>
                  </div>

                  <div class="mindmap-container">
                    <h3>🧠 Java 17 特性知识体系图</h3>
                    <div class="mindmap-wrapper">
                      <div id="chapter3-mindmap" class="mermaid-container"></div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>
          </main>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'
import Java17FeaturesDemo from '@/components/Java17FeaturesDemo.vue'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)
const showNotes = ref(false)
const showQuizModal = ref(false)
const selectedTextDemo = ref(0)
const selectedSwitchExample = ref(0)

// 课程主题
const courseTopics = [
  {
    title: '文本块',
    description: '三引号语法，简化多行字符串处理',
  },
  {
    title: 'Switch表达式',
    description: '从语句到表达式，支持返回值和详尽性检查',
  },
  {
    title: '记录类',
    description: '极简数据类，自动生成样板代码',
  },
  {
    title: '密封类型',
    description: '精确控制继承体系，构建封闭类型层次',
  },
  {
    title: '模式匹配',
    description: 'instanceof增强和Switch模式匹配预览',
  },
]

// 数据定义
const textBlocksData = {
  keyPoints: [
    '三引号语法："""..."""',
    '智能缩进处理：自动去除公共前导空格',
    '自然换行：无需\\n转义',
    '提升可读性：所见即所得的字符串表示',
  ],
}

const switchExpressionsData = {
  keyPoints: [
    '表达式返回值：可以直接赋值给变量',
    '箭头语法：case L -> expression，无需break',
    '多值匹配：case 1, 2, 3 -> ...',
    '详尽性检查：编译器强制覆盖所有情况',
    'yield关键字：在代码块中返回值',
  ],
}

const recordsData = {
  keyPoints: [
    '极简语法：一行代码定义完整数据类',
    '自动生成：构造器、访问器、equals、hashCode、toString',
    '不可变性：所有字段都是final',
    '紧凑构造器：用于参数验证',
    '与密封类型协同：构建强大的数据模型',
  ],
}

const sealedTypesData = {
  keyPoints: [
    'permits关键字：明确指定允许的子类',
    '子类义务：必须是final、sealed或non-sealed',
    '编译时安全：确保类型层次的完整性',
    '与Switch协同：实现详尽的模式匹配',
    '代数数据类型：函数式编程的基础',
  ],
}

const patternMatchingData = {
  keyPoints: [
    'instanceof模式匹配：消除强制转换',
    'Switch模式匹配：类型安全的分支处理',
    '模式变量：自动类型推断和绑定',
    '与Records协同：解构式数据访问',
    '未来发展：向更强大的模式匹配演进',
  ],
}

const chapterSummaryData = {
  keyPoints: [
    '掌握Java 17的五大核心特性',
    '理解现代Java的语言设计理念',
    '学会使用新特性提升代码质量',
    '为函数式编程和模式匹配做准备',
  ],
}

// 文本块演示数据
const textBlockDemos = [
  {
    title: 'SQL查询',
    before: `String query = "SELECT \\"ORDER_ID\\", \\"QUANTITY\\", \\"CURRENCY_PAIR\\" FROM \\"ORDERS\\"\\n" +
               "WHERE \\"CLIENT_ID\\" = ?\\n" +
               "ORDER BY \\"DATE_TIME\\", \\"STATUS\\" LIMIT 100;";`,
    after: `String query = """
    SELECT "ORDER_ID", "QUANTITY", "CURRENCY_PAIR" FROM "ORDERS"
    WHERE "CLIENT_ID" = ?
    ORDER BY "DATE_TIME", "STATUS" LIMIT 100;
    """;`,
    explanation: '消除了转义地狱和字符串拼接，SQL查询清晰可读，就像在SQL编辑器中一样。',
  },
  {
    title: 'JSON数据',
    before: `String json = "{\\n" +
    "  \\"name\\": \\"John\\",\\n" +
    "  \\"age\\": 30,\\n" +
    "  \\"city\\": \\"New York\\"\\n" +
    "}";`,
    after: `String json = """
    {
      "name": "John",
      "age": 30,
      "city": "New York"
    }
    """;`,
    explanation: 'JSON结构一目了然，无需转义引号，格式化自然美观。',
  },
  {
    title: 'HTML模板',
    before: `String html = "<html>\\n" +
    "  <body>\\n" +
    "    <h1>Welcome</h1>\\n" +
    "    <p>Hello, " + name + "!</p>\\n" +
    "  </body>\\n" +
    "</html>";`,
    after: `String html = """
    <html>
      <body>
        <h1>Welcome</h1>
        <p>Hello, %s!</p>
      </body>
    </html>
    """.formatted(name);`,
    explanation: 'HTML结构清晰，配合formatted()方法实现模板功能。',
  },
]

// Switch表达式示例数据
const switchExamples = [
  {
    title: '季节判断',
    traditional: `String season;
switch (month) {
    case 12:
    case 1:
    case 2:
        season = "Winter";
        break;
    case 3:
    case 4:
    case 5:
        season = "Spring";
        break;
    case 6:
    case 7:
    case 8:
        season = "Summer";
        break;
    case 9:
    case 10:
    case 11:
        season = "Autumn";
        break;
    default:
        season = "Unknown";
        break;
}`,
    modern: `String season = switch (month) {
    case 12, 1, 2 -> "Winter";
    case 3, 4, 5 -> "Spring";
    case 6, 7, 8 -> "Summer";
    case 9, 10, 11 -> "Autumn";
    default -> "Unknown";
};`,
    problems: ['需要break语句', '容易case穿透', '样板代码多', '变量需要预声明'],
    benefits: ['无需break', '多值匹配', '直接赋值', '代码简洁'],
    explanation:
      '新的Switch表达式将20多行代码压缩到6行，消除了break语句和case穿透的风险，支持多值匹配，可以直接赋值给变量。',
  },
  {
    title: 'HTTP状态码',
    traditional: `String message;
switch (statusCode) {
    case 200:
        message = "OK";
        break;
    case 404:
        message = "Not Found";
        break;
    case 500:
        message = "Internal Server Error";
        break;
    default:
        message = "Unknown Status";
        break;
}`,
    modern: `String message = switch (statusCode) {
    case 200 -> "OK";
    case 404 -> "Not Found";
    case 500 -> "Internal Server Error";
    default -> "Unknown Status";
};`,
    problems: ['重复的break', '变量预声明', '冗长的语法'],
    benefits: ['简洁语法', '直接返回', '类型安全'],
    explanation:
      'HTTP状态码处理变得极其简洁，每个case都直接返回对应的消息，无需担心忘记break导致的错误。',
  },
  {
    title: 'yield关键字',
    traditional: `int result;
switch (operation) {
    case "add":
        System.out.println("Performing addition");
        result = a + b;
        break;
    case "multiply":
        System.out.println("Performing multiplication");
        result = a * b;
        break;
    default:
        System.out.println("Unknown operation");
        result = 0;
        break;
}`,
    modern: `int result = switch (operation) {
    case "add" -> {
        System.out.println("Performing addition");
        yield a + b;
    }
    case "multiply" -> {
        System.out.println("Performing multiplication");
        yield a * b;
    }
    default -> {
        System.out.println("Unknown operation");
        yield 0;
    }
};`,
    problems: ['复杂的break逻辑', '变量作用域问题', '容易遗漏break'],
    benefits: ['yield明确返回', '代码块支持', '作用域清晰'],
    explanation:
      '当需要在case中执行多条语句时，可以使用代码块配合yield关键字，既保持了代码的清晰性，又确保了正确的返回值。',
  },
]

// 方法
const scrollToTopic = (index: number) => {
  currentTopic.value = index

  // 滚动到对应的主题部分
  const targetElement = document.getElementById(`topic-${index}`)
  if (targetElement) {
    targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  }
}

const toggleNotes = () => {
  showNotes.value = !showNotes.value
}

const showQuiz = () => {
  showQuizModal.value = true
}

const handleInteraction = (type: string) => {
  console.log('Interaction:', type)
}

// 滚动监听函数
const handleScroll = () => {
  const sections = courseTopics
    .map((_, index) => document.getElementById(`topic-${index}`))
    .filter(Boolean)

  if (sections.length === 0) return

  // 获取当前滚动位置
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const windowHeight = window.innerHeight

  // 找到当前可视区域内的章节
  let activeIndex = 0
  for (let i = 0; i < sections.length; i++) {
    const section = sections[i]
    if (section) {
      const rect = section.getBoundingClientRect()
      const sectionTop = rect.top + scrollTop

      // 如果章节顶部在视窗上半部分，则认为是当前活跃章节
      if (sectionTop <= scrollTop + windowHeight * 0.3) {
        activeIndex = i
      } else {
        break
      }
    }
  }

  currentTopic.value = activeIndex
}

// 初始化Mermaid
onMounted(async () => {
  // 添加滚动监听
  window.addEventListener('scroll', handleScroll, { passive: true })

  // 初始化当前章节
  handleScroll()

  try {
    console.log('开始初始化 Mermaid...')
    const mermaid = await import('mermaid')
    console.log('Mermaid 模块加载成功:', mermaid)

    mermaid.default.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
    })
    console.log('Mermaid 初始化完成')

    // 延迟渲染以确保DOM已加载
    setTimeout(async () => {
      try {
        console.log('开始渲染 Mermaid 图表...')

        // 创建思维导图内容
        const mindmapContent = `mindmap
  root((Java 17特性))
    文本块
      三引号语法
      智能缩进
      所见即所得
      多行字符串
    Switch表达式
      箭头语法
      返回值
      详尽性检查
      yield关键字
    记录类
      极简语法
      自动生成
      不可变性
      数据载体
    密封类型
      permits声明
      继承控制
      类型安全
      代数数据类型
    模式匹配
      instanceof增强
      Switch模式
      类型安全
      未来发展`

        const container = document.getElementById('chapter3-mindmap')
        if (container) {
          console.log('找到容器，开始渲染...')
          const { svg } = await mermaid.default.render('chapter3-mindmap-svg', mindmapContent)
          container.innerHTML = svg
          console.log('Mermaid 图表渲染完成')
        } else {
          console.error('未找到思维导图容器')
        }
      } catch (renderError) {
        console.error('Mermaid 渲染错误:', renderError)
      }
    }, 1000) // 增加延迟时间
  } catch (error) {
    console.error('Mermaid 初始化失败:', error)
  }
})

onUnmounted(() => {
  // 移除滚动监听
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
/* 基础样式 */
.java-chapter3 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 0;
  text-align: center;
}

.chapter-title {
  font-size: 2.5rem;
  margin: 0 0 1rem 0;
  font-weight: 700;
}

.chapter-subtitle {
  font-size: 1.2rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
}

.progress-bar {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  height: 8px;
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.progress-fill {
  background: white;
  height: 100%;
  border-radius: 25px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -30px;
  right: 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  margin-top: 3rem;
}

.sidebar {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.outline {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.outline h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
}

.outline-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 0.5rem;
}

.outline-item:hover {
  background: #f8f9fa;
}

.outline-item.active {
  background: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.outline-number {
  width: 30px;
  height: 30px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.outline-content h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.outline-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tool-button {
  background: white;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.tool-button:hover {
  background: #f8f9fa;
  border-color: #667eea;
}

.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.topic-section {
  margin-bottom: 2rem;
}

.topic-section:last-child {
  margin-bottom: 0;
}

/* 特性展示通用样式 */
.text-blocks-showcase,
.switch-expressions-showcase,
.records-showcase,
.sealed-types-showcase,
.pattern-matching-showcase {
  padding: 2rem;
}

.feature-intro {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.intro-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 4px solid #667eea;
}

.intro-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.intro-icon {
  font-size: 1.5rem;
}

.intro-header h4 {
  margin: 0;
  color: #333;
}

.pain-points,
.advantages {
  list-style: none;
  padding: 0;
  margin: 0;
}

.pain-points li,
.advantages li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #e9ecef;
}

.pain-points li:last-child,
.advantages li:last-child {
  border-bottom: none;
}

.pain-points li {
  color: #dc3545;
}

.advantages li {
  color: #28a745;
}

/* 演示区域样式 */
.syntax-demo {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.tab-btn {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.tab-btn:hover {
  background: #e9ecef;
}

.tab-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.before-after {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.before,
.after {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
}

.before {
  border-left: 4px solid #dc3545;
}

.after {
  border-left: 4px solid #28a745;
}

.before h5 {
  color: #dc3545;
  margin: 0 0 1rem 0;
  text-align: center;
}

.after h5 {
  color: #28a745;
  margin: 0 0 1rem 0;
  text-align: center;
}

.code-block {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-width: 100%;
  box-sizing: border-box;
}

.explanation {
  background: #e3f2fd;
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid #2196f3;
}

.explanation h5 {
  margin: 0 0 1rem 0;
  color: #1976d2;
}

.explanation p {
  margin: 0;
  color: #333;
  line-height: 1.6;
}

/* Switch表达式特殊样式 */
.evolution-timeline {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin: 2rem 0;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.timeline-item {
  flex: 1;
  text-align: center;
}

.timeline-marker {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.2rem;
  margin: 0 auto 1rem auto;
}

.timeline-marker.old {
  background: #dc3545;
  color: white;
}

.timeline-marker.new {
  background: #28a745;
  color: white;
}

.timeline-arrow {
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

.timeline-content h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.timeline-content ul {
  text-align: left;
  margin: 0;
  padding-left: 1rem;
}

.syntax-features {
  margin: 2rem 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.feature-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.feature-card h5 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.feature-card code {
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
  display: block;
  margin: 0.5rem 0;
}

.feature-card p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

/* Switch示例样式 */
.code-examples {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.examples-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.example-tab {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.example-tab:hover {
  background: #e9ecef;
}

.example-tab.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.code-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.traditional-code,
.modern-code {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
}

.traditional-code {
  border-left: 4px solid #dc3545;
}

.modern-code {
  border-left: 4px solid #28a745;
}

.traditional-code h5 {
  color: #dc3545;
  margin: 0 0 1rem 0;
  text-align: center;
}

.modern-code h5 {
  color: #28a745;
  margin: 0 0 1rem 0;
  text-align: center;
}

.code-problems,
.code-benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.example-explanation {
  background: #e3f2fd;
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid #2196f3;
}

.example-explanation h5 {
  margin: 0 0 1rem 0;
  color: #1976d2;
}

.example-explanation p {
  margin: 0;
  color: #333;
  line-height: 1.6;
}

/* Records特殊样式 */
.records-concept {
  margin: 2rem 0;
}

.concept-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 2rem;
}

.concept-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.concept-icon {
  font-size: 2rem;
}

.concept-header h4 {
  margin: 0;
  font-size: 1.5rem;
}

.concept-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.philosophy {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.phil-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 8px;
}

.phil-icon {
  font-size: 1.5rem;
}

.records-comparison {
  margin: 2rem 0;
}

.comparison-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  margin-top: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  min-width: 200px;
}

.stat-card.traditional {
  border-left: 4px solid #dc3545;
}

.stat-card.record {
  border-left: 4px solid #28a745;
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-card.traditional .stat-number {
  color: #dc3545;
}

.stat-card.record .stat-number {
  color: #28a745;
}

.stat-label {
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.stat-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-details span {
  font-size: 0.9rem;
  color: #666;
}

.vs-arrow {
  font-size: 2rem;
  font-weight: bold;
  color: #667eea;
}

.records-features {
  margin: 2rem 0;
}

.advanced-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1.5rem;
}

.feature-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 4px solid #667eea;
}

.feature-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.feature-emoji {
  font-size: 1.5rem;
}

.feature-title span:last-child {
  font-weight: 600;
  color: #333;
}

.feature-desc p {
  margin: 0 0 1rem 0;
  color: #666;
  line-height: 1.6;
}

.mini-code {
  background: #2d3748;
  color: #e2e8f0;
  padding: 0.75rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-width: 100%;
}

/* 密封类型特殊样式 */
.inheritance-control {
  margin: 2rem 0;
}

.control-spectrum {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.control-option {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.control-option.old {
  border-left: 4px solid #dc3545;
}

.control-option.new {
  border-left: 4px solid #28a745;
}

.control-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.control-icon {
  font-size: 2rem;
}

.control-header span:last-child {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
}

.control-desc p {
  margin: 0 0 0.5rem 0;
  color: #666;
}

.control-problem {
  color: #dc3545;
  font-weight: 500;
  font-size: 0.9rem;
}

.control-benefit {
  color: #28a745;
  font-weight: 500;
  font-size: 0.9rem;
}

.sealed-rules {
  margin: 2rem 0;
}

.rules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.rule-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 1rem;
}

.rule-number {
  width: 40px;
  height: 40px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.rule-content h5 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.rule-content p {
  margin: 0 0 0.5rem 0;
  color: #666;
  line-height: 1.6;
}

.rule-content code {
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

/* 模式匹配特殊样式 */
.pattern-evolution {
  margin: 2rem 0;
}

.evolution-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.step-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.step-card.current {
  border-left: 4px solid #28a745;
}

.step-card.preview {
  border-left: 4px solid #ffc107;
}

.step-card.future {
  border-left: 4px solid #6c757d;
}

.step-header {
  margin-bottom: 1rem;
}

.step-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.step-card.current .step-badge {
  background: #28a745;
  color: white;
}

.step-card.preview .step-badge {
  background: #ffc107;
  color: #333;
}

.step-card.future .step-badge {
  background: #6c757d;
  color: white;
}

.step-header h5 {
  margin: 0;
  color: #333;
}

.step-code {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
  margin-bottom: 1rem;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-width: 100%;
  box-sizing: border-box;
}

.step-content p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

.pattern-benefits {
  margin: 2rem 0;
}

.benefits-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.benefit-item {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 1rem;
}

.benefit-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.benefit-content h5 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.benefit-content p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

/* 章节总结样式 */
.chapter-summary {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  margin: 2rem 0;
}

.summary-content {
  padding: 2rem;
}

.key-takeaways {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.takeaway-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.takeaway-item:hover {
  transform: translateY(-2px);
}

.takeaway-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.takeaway-item h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.takeaway-item p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

/* 思维导图样式 */
.mindmap-container {
  margin-top: 3rem;
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.mindmap-container h3 {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
}

.mindmap-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 600px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.mermaid-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Mermaid图表样式覆盖 */
.mindmap-wrapper .mermaid {
  max-width: 100%;
  height: auto;
}

.mindmap-wrapper svg {
  max-width: 100%;
  height: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .sidebar {
    position: static;
    order: -1;
  }

  .chapter-title {
    font-size: 2rem;
  }

  .feature-intro {
    grid-template-columns: 1fr;
  }

  .before-after {
    grid-template-columns: 1fr;
  }

  .evolution-timeline {
    flex-direction: column;
    gap: 1rem;
  }

  .timeline-arrow {
    transform: rotate(90deg);
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .comparison-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .vs-arrow {
    transform: rotate(90deg);
  }

  .advanced-features {
    grid-template-columns: 1fr;
  }

  .control-spectrum {
    flex-direction: column;
  }

  .rules-grid {
    grid-template-columns: 1fr;
  }

  .evolution-steps {
    grid-template-columns: 1fr;
  }

  .benefits-showcase {
    grid-template-columns: 1fr;
  }

  .key-takeaways {
    grid-template-columns: 1fr;
  }

  .mindmap-wrapper {
    min-height: 400px;
  }

  .takeaway-item {
    flex-direction: column;
    text-align: center;
  }

  .takeaway-icon {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }

  .chapter-title {
    font-size: 1.8rem;
  }

  .chapter-subtitle {
    font-size: 1rem;
  }

  .demo-tabs {
    flex-direction: column;
  }

  .tab-btn {
    text-align: center;
  }

  .code-comparison {
    grid-template-columns: 1fr;
  }

  .examples-tabs {
    flex-direction: column;
  }

  .code-block,
  .mini-code,
  .step-code {
    font-size: 0.75rem;
    padding: 0.75rem;
  }

  .advanced-features {
    grid-template-columns: 1fr;
  }

  .feature-item {
    margin-bottom: 1rem;
  }

  /* 项目实践踩坑响应式 */
  .problem-cards {
    grid-template-columns: 1fr;
  }

  .solutions-grid {
    grid-template-columns: 1fr;
  }

  .pros-cons {
    grid-template-columns: 1fr;
  }

  .solution-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* 项目实践踩坑与解决方案样式 */
.real-world-problems {
  margin-top: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  border-radius: 12px;
  border-left: 5px solid #ff9800;
}

.real-world-problems h3 {
  margin: 0 0 2rem 0;
  color: #e65100;
  font-size: 1.5rem;
  font-weight: 700;
}

.problem-section,
.root-cause-analysis,
.solutions-section {
  margin-bottom: 2.5rem;
}

.problem-section h4,
.root-cause-analysis h4,
.solutions-section h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.problem-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.problem-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #f44336;
}

.problem-card.warning {
  border-left-color: #ff9800;
}

.problem-card.critical {
  border-left-color: #f44336;
}

.problem-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.problem-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.problem-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.problem-content p {
  margin: 0.75rem 0;
  line-height: 1.6;
  color: #555;
}

.problem-content code {
  background: #f5f5f5;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.cause-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cause-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.cause-item h5 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.cause-item p {
  margin: 0;
  line-height: 1.6;
  color: #555;
}

.solutions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.solution-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-top: 4px solid #4caf50;
}

.solution-card.alternative {
  border-top-color: #2196f3;
}

.solution-card.temporary {
  border-top-color: #ff9800;
}

.solution-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.solution-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.solution-icon {
  font-size: 1.5rem;
}

.solution-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.solution-badge.best {
  background: #e8f5e8;
  color: #2e7d32;
}

.solution-badge.stable {
  background: #e3f2fd;
  color: #1565c0;
}

.solution-badge.conservative {
  background: #f3e5f5;
  color: #7b1fa2;
}

.solution-content p {
  margin: 0 0 1.5rem 0;
  line-height: 1.6;
  color: #555;
}

.pros-cons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.pros,
.cons {
  padding: 1rem;
  border-radius: 8px;
}

.pros {
  background: #e8f5e8;
  border-left: 3px solid #4caf50;
}

.cons {
  background: #ffebee;
  border-left: 3px solid #f44336;
}

.pros h6,
.cons h6 {
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.pros ul,
.cons ul {
  margin: 0;
  padding-left: 1.2rem;
  list-style-type: disc;
}

.pros li,
.cons li {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

.trade-offs {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #6c757d;
}

.trade-offs p {
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #495057;
}
</style>
