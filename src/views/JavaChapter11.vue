<template>
  <div class="java-chapter11">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <div class="chapter-info">
            <h1 class="chapter-title">第十一章：Building with <PERSON><PERSON><PERSON> and <PERSON><PERSON></h1>
            <p class="chapter-subtitle">使用 Gradle 和 Maven 进行构建</p>
            <p class="chapter-description">
              掌控项目的"中枢神经系统"：深入对比Maven和Gradle，揭示构建工具在解决实际工程问题时的不同哲学与实践
            </p>
          </div>
          <div class="progress-indicator">
            <div class="progress-circle">
              <svg class="progress-ring" width="120" height="120">
                <circle
                  class="progress-ring-circle"
                  stroke="rgba(255,255,255,0.3)"
                  stroke-width="4"
                  fill="transparent"
                  r="52"
                  cx="60"
                  cy="60"
                />
                <circle
                  class="progress-ring-circle progress-ring-circle-fill"
                  stroke="white"
                  stroke-width="4"
                  fill="transparent"
                  r="52"
                  cx="60"
                  cy="60"
                  :stroke-dasharray="`${2 * Math.PI * 52}`"
                  :stroke-dashoffset="`${2 * Math.PI * 52 * (1 - progress / 100)}`"
                />
              </svg>
              <div class="progress-text">{{ Math.round(progress) }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: 构建工具的必要性 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="构建工具的必要性 (The Necessity of Build Tools)"
                :concept-data="buildToolsNecessityData"
                @interaction="handleInteraction"
              >
                <div class="build-tools-necessity-showcase">
                  <h3>🎯 构建工具：项目的"中枢神经系统"</h3>
                  <p class="intro-text">
                    构建工具是用于将源代码转换成可执行软件，并自动化该过程中相关任务（如编译、测试、打包、部署）的程序。
                  </p>

                  <div class="human-explanation">
                    <h4>💡 人话版解释</h4>
                    <p class="explanation-text">
                      构建工具就是一个<strong>"全自动工程管家"</strong>。你把原材料（源代码）给它，它就能按照一套标准流程，
                      自动帮你盖好房子（可执行程序），并且还能顺便把装修（打包）、质检（测试）、甚至物流（部署）都给办了。
                    </p>
                  </div>

                  <div class="core-problems">
                    <h4>🚨 现代软件开发的三大根本问题</h4>
                    <div class="problems-grid">
                      <div class="problem-card">
                        <div class="problem-header">
                          <span class="problem-icon">🔥</span>
                          <h5>依赖地狱 (Dependency Hell)</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            一个项目可能依赖几十上百个第三方库（<code>.jar</code>包），这些库之间又互相依赖。
                            手动下载和管理这些jar包的版本，并处理它们之间的冲突，是一场噩梦。
                          </p>
                          <div class="example">
                            <strong>例子：</strong>项目A依赖guava 30.0，项目B依赖guava
                            28.0，当它们合并时会产生版本冲突。
                          </div>
                        </div>
                      </div>

                      <div class="problem-card">
                        <div class="problem-header">
                          <span class="problem-icon">🔄</span>
                          <h5>重复且易错的工作流</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            编译、运行单元测试、打包、生成文档等，这些都是每次代码变更后都需要重复执行的步骤。
                            手动执行不仅效率低下，而且极易出错（例如，忘记编译某个文件）。
                          </p>
                          <div class="example">
                            <strong>例子：</strong
                            >开发者忘记运行测试就提交代码，导致CI/CD流水线失败。
                          </div>
                        </div>
                      </div>

                      <div class="problem-card">
                        <div class="problem-header">
                          <span class="problem-icon">⚠️</span>
                          <h5>环境不一致</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            开发者A的电脑上能成功编译，但在开发者B的电脑上或者在服务器上就失败了。
                            这通常是因为JDK版本、依赖库版本、环境变量等不一致造成的。
                          </p>
                          <div class="example">
                            <strong>例子：</strong>"在我机器上能跑"成为开发团队的经典问题。
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="core-principles">
                    <h4>🏗️ 构建工具的核心原理</h4>
                    <div class="principles-grid">
                      <div class="principle-card">
                        <div class="principle-header">
                          <span class="principle-icon">📋</span>
                          <h5>声明式配置</h5>
                        </div>
                        <p>
                          你只需要在配置文件中"声明"你的直接依赖及其版本，构建工具会自动分析整个依赖树，
                          下载所有需要的传递性依赖，并提供一套机制来解决版本冲突。
                        </p>
                      </div>

                      <div class="principle-card">
                        <div class="principle-header">
                          <span class="principle-icon">🤖</span>
                          <h5>工作流自动化</h5>
                        </div>
                        <p>
                          你通过定义任务（Task）或生命周期（Lifecycle）来"声明"你的构建流程，
                          然后只需一个命令，工具就会自动执行所有步骤。
                        </p>
                      </div>

                      <div class="principle-card">
                        <div class="principle-header">
                          <span class="principle-icon">🔒</span>
                          <h5>一致性保证</h5>
                        </div>
                        <p>
                          构建工具本身（通过其wrapper）和它的配置文件是项目的一部分，随代码一起提交。
                          这保证了任何人在任何机器上，只要使用同一个构建命令，就能得到完全一致的构建环境和结果。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="build-tools-mindmap">
                    <h4>🗺️ 构建工具知识脉络图</h4>
                    <div class="mindmap-container">
                      <div id="build-tools-mindmap" class="mermaid-container">
                        <div class="mindmap-placeholder">
                          <p>🎨 正在生成思维导图...</p>
                          <p>如果长时间未显示，请刷新页面</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="wrapper-solution">
                    <h4>🔧 Wrapper机制：一劳永逸的环境一致性解决方案</h4>
                    <div class="wrapper-explanation">
                      <p>
                        <strong>Maven Wrapper (mvnw) / Gradle Wrapper (gradlew)</strong>：
                        这两个都是官方提供的工具。它会在项目中生成几个脚本文件和一个小的<code>.jar</code>包。
                      </p>
                    </div>
                    <div class="wrapper-mechanism">
                      <h5>工作机制</h5>
                      <div class="mechanism-steps">
                        <div class="step">
                          <span class="step-number">1</span>
                          <p>
                            当你执行 <code>./gradlew build</code> 或 <code>./mvnw package</code> 时
                          </p>
                        </div>
                        <div class="step">
                          <span class="step-number">2</span>
                          <p>脚本会先检查你本地是否已经下载了项目指定的、确切版本的构建工具</p>
                        </div>
                        <div class="step">
                          <span class="step-number">3</span>
                          <p>
                            如果没有，它会<strong>自动下载</strong>这个版本到项目的一个本地目录中
                          </p>
                        </div>
                        <div class="step">
                          <span class="step-number">4</span>
                          <p>然后再用这个下载好的、版本正确的工具来执行构建</p>
                        </div>
                      </div>
                    </div>
                    <div class="wrapper-benefits">
                      <h5>核心优势</h5>
                      <div class="benefits-grid">
                        <div class="benefit-item">
                          <span class="benefit-icon">✅</span>
                          <div>
                            <h6>版本一致性</h6>
                            <p>保证所有开发者使用相同版本的构建工具</p>
                          </div>
                        </div>
                        <div class="benefit-item">
                          <span class="benefit-icon">🚀</span>
                          <div>
                            <h6>零配置上手</h6>
                            <p>新成员无需安装任何构建工具即可开始工作</p>
                          </div>
                        </div>
                        <div class="benefit-item">
                          <span class="benefit-icon">🔒</span>
                          <div>
                            <h6>构建可复现</h6>
                            <p>任何机器上都能得到完全一致的构建结果</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: Maven：约定优于配置 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="Maven：约定优于配置 (Maven: Convention over Configuration)"
                :concept-data="mavenConventionData"
                @interaction="handleInteraction"
              >
                <div class="maven-showcase">
                  <h3>👑 Maven：构建工具界的"霸道总裁"</h3>
                  <p class="intro-text">
                    Maven 是一个强大的项目管理和构建自动化工具。它的核心哲学是<strong
                      >"约定优于配置" (Convention over Configuration)</strong
                    >。
                  </p>

                  <div class="human-explanation">
                    <h4>💡 人话版解释</h4>
                    <p class="explanation-text">
                      Maven 就像一个<strong>"霸道总裁"</strong>。它为你规定好了一切："代码必须放在
                      <code>src/main/java</code>"， "测试必须放在
                      <code>src/test/java</code>"，"构建产物必须输出到 <code>target</code> 目录"，
                      "构建必须先<code>compile</code>再<code>test</code>然后<code>package</code>"。
                      你只要遵守它的这些"约定"，就可以用极少的配置来完成工作。
                    </p>
                  </div>

                  <div class="maven-core-concepts">
                    <h4>🎯 Maven 三大核心概念</h4>
                    <div class="concepts-grid">
                      <div class="concept-card">
                        <div class="concept-header">
                          <span class="concept-icon">📄</span>
                          <h5>项目对象模型 (POM)</h5>
                        </div>
                        <div class="concept-content">
                          <p>
                            所有配置都集中在一个
                            <code>pom.xml</code>
                            文件中。这个文件是对项目元数据（项目名、依赖、插件等）的声明式描述。
                          </p>
                          <div class="code-example">
                            <pre><code>&lt;project&gt;
  &lt;groupId&gt;com.example&lt;/groupId&gt;
  &lt;artifactId&gt;my-app&lt;/artifactId&gt;
  &lt;version&gt;1.0.0&lt;/version&gt;
  &lt;packaging&gt;jar&lt;/packaging&gt;
&lt;/project&gt;</code></pre>
                          </div>
                        </div>
                      </div>

                      <div class="concept-card">
                        <div class="concept-header">
                          <span class="concept-icon">📁</span>
                          <h5>标准目录结构</h5>
                        </div>
                        <div class="concept-content">
                          <p>强制或推荐一套标准的目录布局，确保所有Maven项目都有相同的结构。</p>
                          <div class="directory-structure">
                            <div class="dir-item">📁 src/main/java - 主要源代码</div>
                            <div class="dir-item">📁 src/test/java - 测试代码</div>
                            <div class="dir-item">📁 src/main/resources - 资源文件</div>
                            <div class="dir-item">📁 target - 构建输出</div>
                          </div>
                        </div>
                      </div>

                      <div class="concept-card">
                        <div class="concept-header">
                          <span class="concept-icon">🔄</span>
                          <h5>构建生命周期</h5>
                        </div>
                        <div class="concept-content">
                          <p>
                            定义了一系列有序的、抽象的构建阶段（Phase），如 validate, compile, test,
                            package, install, deploy。
                          </p>
                          <div class="lifecycle-flow">
                            <div class="phase">validate</div>
                            <div class="arrow">→</div>
                            <div class="phase">compile</div>
                            <div class="arrow">→</div>
                            <div class="phase">test</div>
                            <div class="arrow">→</div>
                            <div class="phase">package</div>
                            <div class="arrow">→</div>
                            <div class="phase">install</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="maven-analogy">
                    <h4>🎨 生动类比：Maven的生命周期</h4>
                    <div class="analogy-content">
                      <p>Maven的生命周期就像<strong>宜家家具的安装说明书</strong>：</p>
                      <ul class="analogy-list">
                        <li>
                          <strong>生命周期</strong
                          >：说明书上印好了固定的步骤1、2、3、4（<code>compile</code>,
                          <code>test</code>, <code>package</code>...）
                        </li>
                        <li>
                          <strong>插件目标</strong
                          >：每个步骤具体要干什么（比如步骤1是"用A号螺丝拧紧B号板"），是由具体的"插件"决定的
                        </li>
                        <li>
                          <strong>pom.xml</strong
                          >：就是你的零件清单和一些个性化需求（比如"我不想用标配的把手，想换个铜的"）
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div class="maven-lifecycle-demo">
                    <h4>🔄 Maven生命周期实例演示</h4>
                    <div class="lifecycle-explanation">
                      <p>
                        当你在命令行执行 <code>mvn package</code> 时，Maven
                        并不是只执行"打包"这一个动作。
                        它会严格按照生命周期，<strong>从头开始</strong>依次执行所有在
                        <code>package</code> 之前的阶段。
                      </p>
                    </div>
                    <div class="lifecycle-demo">
                      <div class="lifecycle-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                          <h6>validate</h6>
                          <p>验证项目结构和配置</p>
                        </div>
                      </div>
                      <div class="step-arrow">↓</div>
                      <div class="lifecycle-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                          <h6>compile</h6>
                          <p>编译主要源代码</p>
                        </div>
                      </div>
                      <div class="step-arrow">↓</div>
                      <div class="lifecycle-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                          <h6>test</h6>
                          <p>运行单元测试</p>
                        </div>
                      </div>
                      <div class="step-arrow">↓</div>
                      <div class="lifecycle-step active">
                        <div class="step-number">4</div>
                        <div class="step-content">
                          <h6>package</h6>
                          <p>打包成JAR/WAR文件</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="maven-pitfalls">
                    <h4>⚠️ 常见理论误区与注意事项</h4>
                    <div class="pitfalls-grid">
                      <div class="pitfall-card">
                        <div class="pitfall-header">
                          <span class="pitfall-icon">❌</span>
                          <h5>误区</h5>
                        </div>
                        <p>
                          认为执行 <code>mvn test</code> 就只会运行测试。实际上，它会先执行
                          <code>test</code> 之前的所有阶段，包括 <code>compile</code>。
                        </p>
                      </div>
                      <div class="pitfall-card">
                        <div class="pitfall-header">
                          <span class="pitfall-icon">⚠️</span>
                          <h5>注意</h5>
                        </div>
                        <p>
                          XML 的冗长是 Maven 最大的痛点之一。对于复杂的构建逻辑，<code
                            >pom.xml</code
                          >
                          会变得非常庞大且难以阅读。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="real-world-problems">
                    <h3>🚨 项目实践踩坑与解决方案</h3>
                    <div class="problem-solution-card">
                      <div class="problem-section">
                        <h4>常见问题描述</h4>
                        <p>
                          在一个大型多模块（Multi-module）Maven 项目中，不同的子模块（如
                          <code>module-a</code>, <code>module-b</code>） 都依赖了
                          <code>guava</code> 和 <code>httpclient</code> 这两个库，但它们在各自的
                          <code>pom.xml</code> 中指定的版本不一致。
                          这导致了整个项目依赖混乱，构建结果不可预测，并且在运行时频繁出现
                          <code>NoSuchMethodError</code>。
                        </p>
                      </div>

                      <div class="root-cause-section">
                        <h4>问题根源分析</h4>
                        <p>
                          <strong>缺乏统一的依赖版本管理</strong
                          >。在多模块项目中，如果每个子模块都自己定义依赖版本，就会产生版本冲突。
                          Maven 的"就近原则"(nearest wins)
                          解决冲突的策略，会因为模块依赖关系的变化而产生不同的仲裁结果，使得构建非常脆弱。
                        </p>
                      </div>

                      <div class="solution-section">
                        <h4>业界主流解决方案与权衡</h4>
                        <div class="solution-demo">
                          <h5>方案：使用 &lt;dependencyManagement&gt; 标签</h5>
                          <ol class="solution-steps">
                            <li>
                              在<strong>父 POM</strong> 中，使用
                              <code>&lt;dependencyManagement&gt;</code>
                              标签来<strong>声明</strong>整个项目所有可能用到的依赖及其<strong>统一的版本号</strong>。
                            </li>
                            <li>
                              在各个<strong>子模块</strong>的
                              <code>pom.xml</code> 中，当需要使用某个依赖时，只需要提供
                              <code>groupId</code> 和 <code>artifactId</code>，<strong
                                >不需要也不能指定 version</strong
                              >。
                            </li>
                          </ol>

                          <div class="code-examples">
                            <div class="code-example-section">
                              <h6>父 POM 配置</h6>
                              <div class="code-example">
                                <pre><code>&lt;dependencyManagement&gt;
  &lt;dependencies&gt;
    &lt;dependency&gt;
      &lt;groupId&gt;com.google.guava&lt;/groupId&gt;
      &lt;artifactId&gt;guava&lt;/artifactId&gt;
      &lt;version&gt;31.1-jre&lt;/version&gt;
    &lt;/dependency&gt;
    &lt;dependency&gt;
      &lt;groupId&gt;org.apache.httpcomponents&lt;/groupId&gt;
      &lt;artifactId&gt;httpclient&lt;/artifactId&gt;
      &lt;version&gt;4.5.13&lt;/version&gt;
    &lt;/dependency&gt;
  &lt;/dependencies&gt;
&lt;/dependencyManagement&gt;</code></pre>
                              </div>
                            </div>

                            <div class="code-example-section">
                              <h6>子模块配置</h6>
                              <div class="code-example">
                                <pre><code>&lt;dependencies&gt;
  &lt;dependency&gt;
    &lt;groupId&gt;com.google.guava&lt;/groupId&gt;
    &lt;artifactId&gt;guava&lt;/artifactId&gt;
    &lt;!-- 版本号自动从父POM继承 --&gt;
  &lt;/dependency&gt;
  &lt;dependency&gt;
    &lt;groupId&gt;org.apache.httpcomponents&lt;/groupId&gt;
    &lt;artifactId&gt;httpclient&lt;/artifactId&gt;
    &lt;!-- 版本号自动从父POM继承 --&gt;
  &lt;/dependency&gt;
&lt;/dependencies&gt;</code></pre>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="tradeoffs">
                          <h5>利弊与权衡</h5>
                          <div class="tradeoffs-grid">
                            <div class="pros">
                              <h6>✅ 优点</h6>
                              <ul>
                                <li>
                                  <strong>集中管理了所有依赖的版本</strong
                                  >，保证了整个项目的版本一致性
                                </li>
                                <li>子模块的 POM 变得更干净</li>
                                <li>升级依赖时，只需要修改父 POM 的一处地方即可</li>
                                <li>这套机制也被称为 <strong>BOM (Bill of Materials)</strong></li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 缺点</h6>
                              <ul>
                                <li>需要一个良好的父子模块结构设计</li>
                                <li>增加了项目结构的复杂性</li>
                              </ul>
                            </div>
                          </div>
                          <div class="industry-choice">
                            <p>
                              <strong>业界选择：</strong>在任何多模块 Maven 项目中，<strong
                                >使用 <code>&lt;dependencyManagement&gt;</code> 和 BOM
                                是管理依赖版本的标准最佳实践</strong
                              >。 几乎所有的开源框架（如 Spring Boot, Spring Cloud）都提供自己的 BOM
                              文件，供用户方便地导入和管理其庞大的依赖体系。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>
          </main>
        </div>
      </div>
    </div>

    <!-- 返回顶部按钮 -->
    <BackToTopButton />

    <!-- 浮动章节菜单 -->
    <FloatingChapterMenu :chapters="chapterList" current-chapter="11" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'
import BackToTopButton from '@/components/BackToTopButton.vue'
import FloatingChapterMenu from '@/components/FloatingChapterMenu.vue'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)
const showNotes = ref(false)
const showQuizModal = ref(false)

// 课程主题
const courseTopics = [
  {
    title: '构建工具的必要性',
    description: '理解为什么需要构建工具，三大核心职责',
  },
  {
    title: 'Maven：约定优于配置',
    description: '标准化项目结构与固定构建生命周期',
  },
  {
    title: 'Gradle：灵活性与性能',
    description: '通过代码实现灵活构建与性能优化',
  },
  {
    title: '实战对比与最佳实践',
    description: '依赖冲突处理与多模块项目管理',
  },
]

// 章节列表
const chapterList = [
  { number: 1, title: 'Java基础', path: '/chapter1' },
  { number: 2, title: 'Java模块系统', path: '/chapter2' },
  { number: 3, title: 'Java 17新特性', path: '/chapter3' },
  { number: 4, title: '类文件与字节码', path: '/chapter4' },
  { number: 5, title: 'Java并发基础', path: '/chapter5' },
  { number: 6, title: 'JDK并发库', path: '/chapter6' },
  { number: 7, title: 'Java性能优化', path: '/chapter7' },
  { number: 8, title: 'JVM替代语言', path: '/chapter8' },
  { number: 9, title: 'Kotlin', path: '/chapter9' },
  { number: 10, title: 'Clojure', path: '/chapter10' },
  { number: 11, title: '构建工具', path: '/chapter11' },
]

// 数据定义
const buildToolsNecessityData = {
  keyPoints: [
    '构建工具是将源代码转换成可执行软件并自动化相关任务的程序',
    '解决依赖地狱：自动管理几十上百个第三方库及其版本冲突',
    '消除重复易错工作流：自动化编译、测试、打包、部署等步骤',
    '保证环境一致性：通过wrapper和配置文件确保构建结果可复现',
    '核心原理：声明式配置、工作流自动化、一致性保证',
  ],
  interactiveElements: [
    { type: 'dependency-demo', label: '依赖管理演示' },
    { type: 'workflow-demo', label: '构建流程演示' },
    { type: 'wrapper-demo', label: 'Wrapper机制演示' },
  ],
}

const mavenConventionData = {
  keyPoints: [
    'Maven核心哲学：约定优于配置，通过标准化降低学习和维护成本',
    '项目对象模型(POM)：所有配置集中在pom.xml文件中的声明式描述',
    '标准目录结构：强制统一的目录布局，确保项目结构一致性',
    '构建生命周期：固定有序的构建阶段，从validate到deploy',
    '插件机制：通过插件目标绑定到生命周期阶段完成具体工作',
  ],
  interactiveElements: [
    { type: 'pom-demo', label: 'POM配置演示' },
    { type: 'lifecycle-demo', label: '生命周期演示' },
    { type: 'directory-demo', label: '目录结构演示' },
  ],
}

// 方法定义
const scrollToTopic = (index: number) => {
  const element = document.getElementById(`topic-${index}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
    currentTopic.value = index
  }
}

const toggleNotes = () => {
  showNotes.value = !showNotes.value
}

const showQuiz = () => {
  showQuizModal.value = true
}

const handleInteraction = (type: string) => {
  console.log('Interaction:', type)
  // 处理交互逻辑
  switch (type) {
    case 'dependency-demo':
      // 依赖管理演示
      break
    case 'workflow-demo':
      // 构建流程演示
      break
    case 'wrapper-demo':
      // Wrapper机制演示
      break
    case 'pom-demo':
      // POM配置演示
      break
    case 'lifecycle-demo':
      // 生命周期演示
      break
    case 'directory-demo':
      // 目录结构演示
      break
  }
}

// 渲染思维导图
const renderMindMap = async () => {
  try {
    const { default: mermaid } = await import('mermaid')

    mermaid.initialize({
      startOnLoad: false,
      theme: 'default',
      themeVariables: {
        primaryColor: '#667eea',
        primaryTextColor: '#fff',
        primaryBorderColor: '#764ba2',
        lineColor: '#667eea',
        secondaryColor: '#f8f9fa',
        tertiaryColor: '#e9ecef',
      },
    })

    // 构建工具思维导图内容
    const mindmapContent = `mindmap
  root((构建工具))
    必要性
      依赖地狱
        版本冲突
        传递依赖
        手动管理困难
      重复工作流
        编译
        测试
        打包
        部署
      环境不一致
        JDK版本
        依赖版本
        配置差异
    核心原理
      声明式配置
        依赖声明
        自动解析
        冲突处理
      工作流自动化
        生命周期
        任务定义
        插件机制
      一致性保证
        Wrapper机制
        配置文件
        版本锁定
    Maven特点
      约定优于配置
        标准目录
        固定生命周期
        插件绑定
      POM模型
        项目元数据
        依赖管理
        构建配置
      生命周期
        validate
        compile
        test
        package
        install
        deploy
    解决方案
      dependencyManagement
        版本统一
        BOM机制
        父子继承
      Wrapper机制
        版本一致
        自动下载
        零配置`

    const element = document.getElementById('build-tools-mindmap')
    if (element) {
      element.innerHTML = mindmapContent
      await mermaid.run({
        nodes: [element],
      })
    }
  } catch (error) {
    console.error('Failed to render mind map:', error)
    const element = document.getElementById('build-tools-mindmap')
    if (element) {
      element.innerHTML = '<p style="color: #dc3545;">思维导图加载失败，请刷新页面重试</p>'
    }
  }
}

// 滚动监听
const handleScroll = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const windowHeight = window.innerHeight
  const documentHeight = document.documentElement.scrollHeight

  // 计算进度
  progress.value = (scrollTop / (documentHeight - windowHeight)) * 100

  // 更新当前主题
  const topics = document.querySelectorAll('.topic-section')
  topics.forEach((topic, index) => {
    const rect = topic.getBoundingClientRect()
    if (rect.top <= windowHeight / 2 && rect.bottom >= windowHeight / 2) {
      currentTopic.value = index
    }
  })
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  // 渲染思维导图
  setTimeout(() => {
    renderMindMap()
  }, 1000)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.java-chapter11 {
  min-height: 100vh;
  background: #f8f9fa;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 页面头部样式 */
.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 0;
  position: relative;
  overflow: hidden;
}

.chapter-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
    repeat;
  opacity: 0.3;
}

.header-content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 3rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.chapter-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.chapter-subtitle {
  font-size: 1.5rem;
  opacity: 0.9;
  margin-bottom: 1rem;
  font-style: italic;
}

.chapter-description {
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.95;
  max-width: 600px;
}

/* 进度指示器 */
.progress-indicator {
  position: relative;
}

.progress-circle {
  position: relative;
  width: 120px;
  height: 120px;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle-fill {
  transition: stroke-dashoffset 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.2rem;
  font-weight: 600;
}

/* 内容布局 */
.content-wrapper {
  background: #f8f9fa;
  min-height: 100vh;
  padding: 2rem 0;
  overflow-x: hidden;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  align-items: start;
  max-width: 100%;
  box-sizing: border-box;
}

/* 侧边栏样式 */
.sidebar {
  position: sticky;
  top: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.outline {
  padding: 1.5rem;
}

.outline h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.outline-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.outline-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.outline-item:hover {
  background: #f8f9fa;
  border-color: #e9ecef;
}

.outline-item.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: #667eea;
}

.outline-number {
  width: 24px;
  height: 24px;
  background: #e9ecef;
  color: #495057;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.outline-item.active .outline-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.outline-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.outline-content p {
  margin: 0;
  font-size: 0.8rem;
  opacity: 0.8;
  line-height: 1.3;
}

/* 工具栏 */
.toolbar {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 0.5rem;
}

.tool-button {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #dee2e6;
  background: white;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-button:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

/* 主内容区 */
.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.topic-section {
  border-bottom: 1px solid #e9ecef;
}

.topic-section:last-child {
  border-bottom: none;
}

/* 构建工具必要性样式 */
.build-tools-necessity-showcase {
  padding: 2rem;
}

.intro-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #495057;
  margin-bottom: 2rem;
}

.human-explanation {
  background: #e3f2fd;
  border-left: 4px solid #2196f3;
  padding: 1.5rem;
  margin: 2rem 0;
  border-radius: 0 8px 8px 0;
}

.human-explanation h4 {
  margin: 0 0 1rem 0;
  color: #1976d2;
}

.explanation-text {
  margin: 0;
  line-height: 1.6;
  color: #424242;
}

.core-problems {
  margin: 3rem 0;
}

.core-problems h4 {
  margin-bottom: 2rem;
  color: #333;
  font-size: 1.3rem;
}

.problems-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.problem-card {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.problem-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.problem-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.problem-icon {
  font-size: 1.5rem;
}

.problem-header h5 {
  margin: 0;
  color: #c53030;
  font-size: 1.1rem;
}

.problem-content p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
  color: #4a5568;
}

.example {
  background: rgba(197, 48, 48, 0.1);
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #742a2a;
}

.core-principles {
  margin: 3rem 0;
}

.core-principles h4 {
  margin-bottom: 2rem;
  color: #333;
  font-size: 1.3rem;
}

.principles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.principle-card {
  background: #f0fff4;
  border: 1px solid #c6f6d5;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.principle-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.principle-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.principle-icon {
  font-size: 1.5rem;
}

.principle-header h5 {
  margin: 0;
  color: #2f855a;
  font-size: 1.1rem;
}

.principle-card p {
  margin: 0;
  line-height: 1.6;
  color: #4a5568;
}

/* Maven 样式 */
.maven-showcase {
  padding: 2rem;
}

.maven-core-concepts {
  margin: 3rem 0;
}

.maven-core-concepts h4 {
  margin-bottom: 2rem;
  color: #333;
  font-size: 1.3rem;
}

.concepts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.concept-card {
  background: #fefefe;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.concept-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.concept-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.concept-icon {
  font-size: 1.5rem;
  color: #667eea;
}

.concept-header h5 {
  margin: 0;
  color: #2d3748;
  font-size: 1.1rem;
}

.concept-content p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
  color: #4a5568;
}

.code-example {
  background: #1a202c;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.code-example pre {
  margin: 0;
  white-space: pre-wrap;
}

.directory-structure {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.dir-item {
  padding: 0.25rem 0;
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
  color: #4a5568;
}

.lifecycle-flow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin: 1rem 0;
}

.phase {
  background: #667eea;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.arrow {
  color: #667eea;
  font-weight: bold;
}

/* Maven 类比和演示样式 */
.maven-analogy {
  background: #fff8e1;
  border-left: 4px solid #ff9800;
  padding: 1.5rem;
  margin: 2rem 0;
  border-radius: 0 8px 8px 0;
}

.maven-analogy h4 {
  margin: 0 0 1rem 0;
  color: #e65100;
}

.analogy-content p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
  color: #424242;
}

.analogy-list {
  margin: 0;
  padding-left: 1.5rem;
}

.analogy-list li {
  margin-bottom: 0.75rem;
  line-height: 1.6;
  color: #424242;
}

.maven-lifecycle-demo {
  margin: 3rem 0;
}

.maven-lifecycle-demo h4 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.3rem;
}

.lifecycle-explanation {
  background: #f3e5f5;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  border-left: 4px solid #9c27b0;
}

.lifecycle-explanation p {
  margin: 0;
  line-height: 1.6;
  color: #4a148c;
}

.lifecycle-demo {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.lifecycle-step {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  border: 2px solid #e0e0e0;
  min-width: 250px;
  transition: all 0.3s ease;
}

.lifecycle-step.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #e0e0e0;
  color: #424242;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.lifecycle-step.active .step-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.step-content h6 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.step-content p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

.step-arrow {
  font-size: 1.5rem;
  color: #667eea;
  font-weight: bold;
}

/* Maven 误区样式 */
.maven-pitfalls {
  margin: 3rem 0;
}

.maven-pitfalls h4 {
  margin-bottom: 2rem;
  color: #333;
  font-size: 1.3rem;
}

.pitfalls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.pitfall-card {
  background: #fff3e0;
  border: 1px solid #ffcc02;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.pitfall-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.pitfall-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.pitfall-icon {
  font-size: 1.5rem;
}

.pitfall-header h5 {
  margin: 0;
  color: #e65100;
  font-size: 1.1rem;
}

.pitfall-card p {
  margin: 0;
  line-height: 1.6;
  color: #4a5568;
}

/* 实际问题解决方案样式 */
.real-world-problems {
  margin: 4rem 0 2rem 0;
  padding-top: 2rem;
  border-top: 2px solid #e9ecef;
}

.real-world-problems h3 {
  margin-bottom: 2rem;
  color: #dc3545;
  font-size: 1.5rem;
}

.problem-solution-card {
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.problem-section,
.root-cause-section,
.solution-section {
  padding: 2rem;
  border-bottom: 1px solid #dee2e6;
}

.solution-section {
  border-bottom: none;
}

.problem-section {
  background: #fff5f5;
  border-left: 4px solid #dc3545;
}

.root-cause-section {
  background: #fff8e1;
  border-left: 4px solid #ff9800;
}

.solution-section {
  background: #f0fff4;
  border-left: 4px solid #28a745;
}

.problem-section h4,
.root-cause-section h4,
.solution-section h4 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.problem-section h4 {
  color: #dc3545;
}

.root-cause-section h4 {
  color: #e65100;
}

.solution-section h4 {
  color: #28a745;
}

.solution-demo h5 {
  margin: 0 0 1rem 0;
  color: #155724;
  font-size: 1.1rem;
}

.solution-steps {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.solution-steps li {
  margin-bottom: 0.75rem;
  line-height: 1.6;
  color: #424242;
}

.code-examples {
  margin: 2rem 0;
}

.code-example-section {
  margin-bottom: 1.5rem;
}

.code-example-section h6 {
  margin: 0 0 0.5rem 0;
  color: #495057;
  font-size: 1rem;
  font-weight: 600;
}

.tradeoffs {
  margin: 2rem 0;
}

.tradeoffs h5 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
}

.tradeoffs-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.pros,
.cons {
  padding: 1rem;
  border-radius: 8px;
}

.pros {
  background: #d4edda;
  border: 1px solid #c3e6cb;
}

.cons {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
}

.pros h6 {
  margin: 0 0 0.75rem 0;
  color: #155724;
}

.cons h6 {
  margin: 0 0 0.75rem 0;
  color: #721c24;
}

.pros ul,
.cons ul {
  margin: 0;
  padding-left: 1.5rem;
}

.pros li,
.cons li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.pros li {
  color: #155724;
}

.cons li {
  color: #721c24;
}

.industry-choice {
  background: #e3f2fd;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.industry-choice p {
  margin: 0;
  line-height: 1.6;
  color: #0d47a1;
}

/* 思维导图样式 */
.build-tools-mindmap {
  margin: 3rem 0;
}

.build-tools-mindmap h4 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.3rem;
}

.mindmap-container {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.mermaid-container {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mindmap-placeholder {
  text-align: center;
  color: #666;
}

.mindmap-placeholder p {
  margin: 0.5rem 0;
}

/* Wrapper解决方案样式 */
.wrapper-solution {
  margin: 3rem 0;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  border-left: 4px solid #28a745;
}

.wrapper-solution h4 {
  margin: 0 0 1.5rem 0;
  color: #155724;
  font-size: 1.3rem;
}

.wrapper-explanation {
  margin-bottom: 2rem;
}

.wrapper-explanation p {
  margin: 0;
  line-height: 1.6;
  color: #495057;
}

.wrapper-mechanism {
  margin-bottom: 2rem;
}

.wrapper-mechanism h5 {
  margin: 0 0 1rem 0;
  color: #155724;
  font-size: 1.1rem;
}

.mechanism-steps {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.step-number {
  width: 28px;
  height: 28px;
  background: #28a745;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: 600;
  flex-shrink: 0;
}

.step p {
  margin: 0;
  line-height: 1.5;
  color: #495057;
}

.wrapper-benefits h5 {
  margin: 0 0 1rem 0;
  color: #155724;
  font-size: 1.1rem;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #c3e6cb;
}

.benefit-icon {
  font-size: 1.2rem;
  color: #28a745;
  flex-shrink: 0;
}

.benefit-item h6 {
  margin: 0 0 0.25rem 0;
  color: #155724;
  font-size: 0.95rem;
}

.benefit-item p {
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #495057;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .sidebar {
    position: static;
    order: -1;
  }

  .header-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .chapter-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .chapter-title {
    font-size: 2rem;
  }

  .problems-grid,
  .principles-grid,
  .concepts-grid,
  .pitfalls-grid {
    grid-template-columns: 1fr;
  }

  .lifecycle-flow {
    flex-direction: column;
    align-items: stretch;
  }

  .arrow {
    transform: rotate(90deg);
    align-self: center;
  }

  .tradeoffs-grid {
    grid-template-columns: 1fr;
  }

  .lifecycle-step {
    min-width: auto;
  }

  .step-arrow {
    transform: rotate(90deg);
  }

  .code-examples {
    margin: 1rem 0;
  }

  .solution-steps {
    padding-left: 1rem;
  }

  .problem-solution-card .problem-section,
  .problem-solution-card .root-cause-section,
  .problem-solution-card .solution-section {
    padding: 1.5rem;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .mechanism-steps {
    gap: 0.75rem;
  }

  .step {
    padding: 0.75rem;
  }

  .mindmap-container {
    padding: 1rem;
  }

  .wrapper-solution {
    padding: 1.5rem;
  }
}
</style>
