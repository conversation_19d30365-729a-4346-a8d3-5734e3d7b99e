<template>
  <nav class="navigation-bar">
    <div class="nav-content">
      <!-- 分类导航 -->
      <div class="category-nav">
        <div
          class="category-item-wrapper"
          @mouseenter="showCategoryMenu('tech')"
          @mouseleave="handleMenuLeave"
        >
          <div class="category-item">
            <span class="category-title">📚 技术类</span>
            <span class="dropdown-arrow">▼</span>
          </div>

          <!-- 技术类下拉菜单 -->
          <div v-if="activeCategory === 'tech'" class="category-dropdown">
            <div class="subcategory-item" @mouseenter="showSubcategoryMenu('java')">
              <span>☕ Java相关</span>
              <span class="arrow-right">▶</span>

              <!-- Java相关子菜单 -->
              <div v-if="activeSubcategory === 'java'" class="subcategory-dropdown">
                <RouterLink
                  to="/books/well-grounded-java-developer"
                  class="book-item"
                  @click="closeAllMenus"
                >
                  <div class="book-info">
                    <span class="book-icon">📖</span>
                    <div class="book-details">
                      <div class="book-title">The Well-Grounded Java Developer</div>
                      <div class="book-subtitle">Second Edition</div>
                    </div>
                  </div>
                </RouterLink>
              </div>
            </div>

            <div class="subcategory-item">
              <span>🐍 Python相关</span>
              <span class="arrow-right">▶</span>
            </div>

            <div class="subcategory-item">
              <span>⚛️ 前端开发</span>
              <span class="arrow-right">▶</span>
            </div>
          </div>
        </div>

        <div class="category-item-wrapper">
          <div class="category-item">
            <span class="category-title">💼 商业类</span>
            <span class="dropdown-arrow">▼</span>
          </div>
        </div>

        <div class="category-item-wrapper">
          <div class="category-item">
            <span class="category-title">🎨 设计类</span>
            <span class="dropdown-arrow">▼</span>
          </div>
        </div>
      </div>

      <!-- 右侧导航 -->
      <div class="right-nav">
        <RouterLink to="/" class="nav-link">首页</RouterLink>
        <RouterLink to="/about" class="nav-link">关于</RouterLink>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const activeCategory = ref<string | null>(null)
const activeSubcategory = ref<string | null>(null)
let hideTimer: number | null = null

const showCategoryMenu = (category: string) => {
  // 清除任何待执行的隐藏定时器
  if (hideTimer) {
    clearTimeout(hideTimer)
    hideTimer = null
  }
  activeCategory.value = category
}

const handleMenuLeave = () => {
  // 当鼠标离开整个菜单区域时，延迟隐藏菜单
  hideTimer = setTimeout(() => {
    activeCategory.value = null
    activeSubcategory.value = null
  }, 200) // 短延迟，防止意外触发
}

const showSubcategoryMenu = (subcategory: string) => {
  activeSubcategory.value = subcategory
}

const closeAllMenus = () => {
  if (hideTimer) {
    clearTimeout(hideTimer)
    hideTimer = null
  }
  activeCategory.value = null
  activeSubcategory.value = null
}
</script>

<style scoped>
.navigation-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
}

.category-nav {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.category-item-wrapper {
  position: relative;
}

.category-item {
  cursor: pointer;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.category-item:hover,
.category-item-wrapper:hover .category-item {
  background: rgba(255, 255, 255, 0.1);
}

.category-title {
  font-weight: 600;
  font-size: 1rem;
}

.dropdown-arrow {
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.category-item:hover .dropdown-arrow {
  transform: rotate(180deg);
}

.category-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  min-width: 200px;
  z-index: 1001;
  animation: dropdownSlide 0.3s ease-out;
}

.subcategory-item {
  position: relative;
  padding: 0.75rem 1rem;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.subcategory-item:hover {
  background: #f8f9fa;
  color: #667eea;
}

.arrow-right {
  font-size: 0.7rem;
  opacity: 0.6;
}

.subcategory-dropdown {
  position: absolute;
  top: 0;
  left: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  padding: 0.5rem 0;
  margin-left: 0.5rem;
  min-width: 300px;
  z-index: 1002;
  animation: slideInRight 0.3s ease-out;
}

.book-item {
  display: block;
  padding: 1rem;
  color: #333;
  text-decoration: none;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.book-item:hover {
  background: #f8f9fa;
  border-left-color: #667eea;
}

.book-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.book-icon {
  font-size: 1.5rem;
}

.book-details {
  flex: 1;
}

.book-title {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.book-subtitle {
  font-size: 0.85rem;
  opacity: 0.7;
  font-style: italic;
}

.right-nav {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  transition: all 0.3s ease;
  font-weight: 500;
  opacity: 0.9;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  opacity: 1;
  transform: translateY(-2px);
}

.nav-link.router-link-exact-active {
  background: rgba(255, 255, 255, 0.25);
  opacity: 1;
  font-weight: 600;
}

@keyframes dropdownSlide {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@media (max-width: 768px) {
  .nav-content {
    flex-direction: column;
    height: auto;
    padding: 1rem;
    gap: 1rem;
  }

  .category-nav {
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  .category-item {
    padding: 0.75rem 1rem;
  }

  .category-title {
    font-size: 0.9rem;
  }

  .subcategory-dropdown {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin-left: 0;
    max-width: 90vw;
  }
}
</style>
