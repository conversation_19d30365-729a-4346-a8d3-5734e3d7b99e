<template>
  <div class="concurrency-animation">
    <div class="animation-controls">
      <h3>🔄 并发机制演示</h3>
      <p class="animation-description">{{ currentStep.description }}</p>
      <div class="control-buttons">
        <button @click="playAnimation" :disabled="isPlaying" class="play-btn">
          {{ isPlaying ? '⏸️ 暂停' : '▶️ 播放' }}
        </button>
        <button @click="resetAnimation" class="reset-btn">🔄 重置</button>
        <button @click="prevStep" :disabled="currentStepIndex === 0" class="step-btn">
          ⬅️ 上一步
        </button>
        <button
          @click="nextStep"
          :disabled="currentStepIndex === steps.length - 1"
          class="step-btn"
        >
          ➡️ 下一步
        </button>
      </div>
      <div class="step-indicator">步骤 {{ currentStepIndex + 1 }} / {{ steps.length }}</div>
    </div>

    <div class="animation-canvas">
      <svg width="900" height="700" viewBox="0 0 900 700">
        <!-- 背景网格 -->
        <defs>
          <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1" />
          </pattern>
        </defs>
        <rect width="900" height="700" fill="url(#grid)" opacity="0.3" />

        <!-- 主内存区域 -->
        <g class="main-memory" :class="{ highlight: currentStepIndex >= 1 }">
          <rect
            x="50"
            y="50"
            width="800"
            height="150"
            fill="#e3f2fd"
            stroke="#2196f3"
            stroke-width="2"
            rx="10"
          />
          <text x="450" y="80" text-anchor="middle" class="title-text">主内存 (Main Memory)</text>

          <!-- 共享变量 -->
          <g class="shared-variable" :class="{ 'animate-in': currentStepIndex >= 2 }">
            <rect
              x="200"
              y="100"
              width="120"
              height="80"
              fill="#4caf50"
              stroke="#388e3c"
              stroke-width="2"
              rx="5"
            />
            <text x="260" y="130" text-anchor="middle" class="var-text">共享变量</text>
            <text x="260" y="150" text-anchor="middle" class="var-value">
              count = {{ sharedValue }}
            </text>
          </g>

          <g class="shared-variable" :class="{ 'animate-in': currentStepIndex >= 2 }">
            <rect
              x="400"
              y="100"
              width="120"
              height="80"
              fill="#ff9800"
              stroke="#f57c00"
              stroke-width="2"
              rx="5"
            />
            <text x="460" y="130" text-anchor="middle" class="var-text">volatile变量</text>
            <text x="460" y="150" text-anchor="middle" class="var-value">
              flag = {{ volatileFlag }}
            </text>
          </g>

          <g class="shared-variable" :class="{ 'animate-in': currentStepIndex >= 2 }">
            <rect
              x="600"
              y="100"
              width="120"
              height="80"
              fill="#9c27b0"
              stroke="#7b1fa2"
              stroke-width="2"
              rx="5"
            />
            <text x="660" y="130" text-anchor="middle" class="var-text">锁对象</text>
            <text x="660" y="150" text-anchor="middle" class="var-value">{{ lockStatus }}</text>
          </g>
        </g>

        <!-- 线程1 -->
        <g class="thread1" :class="{ 'animate-in': currentStepIndex >= 3 }">
          <rect
            x="100"
            y="250"
            width="200"
            height="180"
            fill="#fff3e0"
            stroke="#ff9800"
            stroke-width="2"
            rx="10"
          />
          <text x="200" y="280" text-anchor="middle" class="thread-title">线程1 (Thread-1)</text>

          <!-- 工作内存 -->
          <rect
            x="120"
            y="300"
            width="160"
            height="100"
            fill="#ffcc80"
            stroke="#ff8f00"
            stroke-width="1"
            rx="5"
          />
          <text x="200" y="320" text-anchor="middle" class="memory-label">工作内存</text>

          <!-- 本地副本 -->
          <rect
            x="130"
            y="330"
            width="60"
            height="30"
            fill="#fff"
            stroke="#ff8f00"
            stroke-width="1"
            rx="3"
          />
          <text x="160" y="350" text-anchor="middle" class="local-var">
            count: {{ thread1Value }}
          </text>

          <rect
            x="210"
            y="330"
            width="60"
            height="30"
            fill="#fff"
            stroke="#ff8f00"
            stroke-width="1"
            rx="3"
          />
          <text x="240" y="350" text-anchor="middle" class="local-var">
            flag: {{ thread1Flag }}
          </text>
        </g>

        <!-- 线程2 -->
        <g class="thread2" :class="{ 'animate-in': currentStepIndex >= 3 }">
          <rect
            x="600"
            y="250"
            width="200"
            height="180"
            fill="#f3e5f5"
            stroke="#9c27b0"
            stroke-width="2"
            rx="10"
          />
          <text x="700" y="280" text-anchor="middle" class="thread-title">线程2 (Thread-2)</text>

          <!-- 工作内存 -->
          <rect
            x="620"
            y="300"
            width="160"
            height="100"
            fill="#e1bee7"
            stroke="#8e24aa"
            stroke-width="1"
            rx="5"
          />
          <text x="700" y="320" text-anchor="middle" class="memory-label">工作内存</text>

          <!-- 本地副本 -->
          <rect
            x="630"
            y="330"
            width="60"
            height="30"
            fill="#fff"
            stroke="#8e24aa"
            stroke-width="1"
            rx="3"
          />
          <text x="660" y="350" text-anchor="middle" class="local-var">
            count: {{ thread2Value }}
          </text>

          <rect
            x="710"
            y="330"
            width="60"
            height="30"
            fill="#fff"
            stroke="#8e24aa"
            stroke-width="1"
            rx="3"
          />
          <text x="740" y="350" text-anchor="middle" class="local-var">
            flag: {{ thread2Flag }}
          </text>
        </g>

        <!-- 同步操作演示 -->
        <g class="sync-demo" :class="{ show: currentStepIndex >= 4 }">
          <rect
            x="350"
            y="500"
            width="200"
            height="120"
            fill="#e8f5e8"
            stroke="#4caf50"
            stroke-width="2"
            rx="10"
          />
          <text x="450" y="530" text-anchor="middle" class="sync-title">同步操作</text>

          <!-- synchronized块 -->
          <rect
            x="370"
            y="550"
            width="160"
            height="60"
            fill="#c8e6c9"
            stroke="#388e3c"
            stroke-width="1"
            rx="5"
          />
          <text x="450" y="575" text-anchor="middle" class="sync-text">synchronized(lock) {</text>
          <text x="450" y="595" text-anchor="middle" class="sync-text">count++;</text>
          <text x="450" y="610" text-anchor="middle" class="sync-text">}</text>
        </g>

        <!-- 数据流箭头 -->
        <g class="data-flow" :class="{ animate: currentStepIndex >= 5 }">
          <!-- 线程1到主内存 -->
          <path
            d="M 200 250 L 260 200"
            stroke="#ff9800"
            stroke-width="3"
            marker-end="url(#orangeArrow)"
          />
          <text x="220" y="220" class="flow-label">写入</text>

          <!-- 主内存到线程2 -->
          <path
            d="M 460 200 L 700 250"
            stroke="#9c27b0"
            stroke-width="3"
            marker-end="url(#purpleArrow)"
          />
          <text x="580" y="220" class="flow-label">读取</text>

          <!-- volatile写入 -->
          <path d="M 460 200 L 460 250" stroke="#f44336" stroke-width="4" stroke-dasharray="5,5" />
          <text x="470" y="225" class="flow-label volatile">volatile写</text>
        </g>

        <!-- 箭头标记定义 -->
        <defs>
          <marker
            id="orangeArrow"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#ff9800" />
          </marker>
          <marker
            id="purpleArrow"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#9c27b0" />
          </marker>
        </defs>
      </svg>
    </div>

    <div class="step-explanation">
      <div class="explanation-card" :class="currentStep.type">
        <h4>{{ currentStep.title }}</h4>
        <p>{{ currentStep.detail }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'

interface AnimationStep {
  id: number
  title: string
  description: string
  detail: string
  type: 'info' | 'success' | 'warning' | 'danger'
}

const currentStepIndex = ref(0)
const isPlaying = ref(false)
const sharedValue = ref(0)
const volatileFlag = ref('false')
const lockStatus = ref('未锁定')
const thread1Value = ref(0)
const thread1Flag = ref('false')
const thread2Value = ref(0)
const thread2Flag = ref('false')

let animationTimer: number | null = null

const steps: AnimationStep[] = [
  {
    id: 0,
    title: '并发环境初始化',
    description: '多线程环境准备就绪',
    detail:
      '在多线程环境中，每个线程都有自己的工作内存，同时共享主内存中的数据。这是并发问题产生的根源。',
    type: 'info',
  },
  {
    id: 1,
    title: '主内存区域',
    description: '所有线程共享的内存区域',
    detail:
      '主内存存储所有共享变量的主副本，是线程间数据交换的中心。所有线程对共享数据的最终修改都要写回主内存。',
    type: 'info',
  },
  {
    id: 2,
    title: '共享变量初始化',
    description: '创建共享的数据和同步原语',
    detail: '普通变量、volatile变量和锁对象都存储在主内存中。它们有不同的可见性和同步保证。',
    type: 'success',
  },
  {
    id: 3,
    title: '线程工作内存',
    description: '每个线程拥有私有的工作内存',
    detail:
      '线程不能直接访问主内存，必须先将数据复制到自己的工作内存中，修改后再写回主内存。这种设计提高了性能但带来了可见性问题。',
    type: 'warning',
  },
  {
    id: 4,
    title: '同步机制',
    description: '使用synchronized确保数据一致性',
    detail:
      'synchronized关键字通过获取对象锁来确保同一时刻只有一个线程能执行临界区代码，从而保证数据的一致性。',
    type: 'success',
  },
  {
    id: 5,
    title: '数据流动',
    description: '观察数据在内存间的流动',
    detail:
      '线程对共享数据的修改需要经过：读取→修改→写回的过程。volatile变量保证修改立即对其他线程可见。',
    type: 'info',
  },
]

const currentStep = computed(() => steps[currentStepIndex.value])

const playAnimation = () => {
  if (isPlaying.value) {
    pauseAnimation()
    return
  }

  isPlaying.value = true
  animationTimer = window.setInterval(() => {
    if (currentStepIndex.value < steps.length - 1) {
      currentStepIndex.value++
      updateValues()
    } else {
      pauseAnimation()
    }
  }, 2500)
}

const pauseAnimation = () => {
  isPlaying.value = false
  if (animationTimer) {
    clearInterval(animationTimer)
    animationTimer = null
  }
}

const resetAnimation = () => {
  pauseAnimation()
  currentStepIndex.value = 0
  resetValues()
}

const nextStep = () => {
  if (currentStepIndex.value < steps.length - 1) {
    currentStepIndex.value++
    updateValues()
  }
}

const prevStep = () => {
  if (currentStepIndex.value > 0) {
    currentStepIndex.value--
    updateValues()
  }
}

const updateValues = () => {
  const step = currentStepIndex.value

  switch (step) {
    case 0:
      resetValues()
      break
    case 1:
      // 主内存高亮
      break
    case 2:
      // 初始化共享变量
      sharedValue.value = 0
      volatileFlag.value = 'false'
      lockStatus.value = '未锁定'
      break
    case 3:
      // 线程读取数据到工作内存
      thread1Value.value = sharedValue.value
      thread1Flag.value = volatileFlag.value
      thread2Value.value = sharedValue.value
      thread2Flag.value = volatileFlag.value
      break
    case 4:
      // 同步操作
      lockStatus.value = '线程1持有'
      thread1Value.value = 1
      break
    case 5:
      // 数据流动
      sharedValue.value = thread1Value.value
      volatileFlag.value = 'true'
      lockStatus.value = '未锁定'
      thread2Value.value = sharedValue.value
      thread2Flag.value = volatileFlag.value
      break
  }
}

const resetValues = () => {
  sharedValue.value = 0
  volatileFlag.value = 'false'
  lockStatus.value = '未锁定'
  thread1Value.value = 0
  thread1Flag.value = 'false'
  thread2Value.value = 0
  thread2Flag.value = 'false'
}

onMounted(() => {
  console.log('并发动画组件已加载')
  resetValues()
})
</script>

<style scoped>
.concurrency-animation {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin: 2rem 0;
}

.animation-controls {
  text-align: center;
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
}

.animation-controls h3 {
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.animation-description {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 1rem;
  font-weight: 500;
}

.control-buttons {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.control-buttons button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.play-btn {
  background: #4caf50;
  color: white;
}

.play-btn:hover:not(:disabled) {
  background: #45a049;
}

.reset-btn {
  background: #ff9800;
  color: white;
}

.reset-btn:hover {
  background: #f57c00;
}

.step-btn {
  background: #2196f3;
  color: white;
}

.step-btn:hover:not(:disabled) {
  background: #1976d2;
}

.step-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.step-indicator {
  font-size: 0.9rem;
  color: #666;
  font-weight: bold;
}

.animation-canvas {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
}

.animation-canvas svg {
  width: 100%;
  height: auto;
  min-width: 900px;
}

/* SVG 样式 */
.title-text {
  font-size: 16px;
  font-weight: bold;
  fill: #2c3e50;
}

.thread-title {
  font-size: 14px;
  font-weight: bold;
  fill: #333;
}

.memory-label {
  font-size: 12px;
  font-weight: bold;
  fill: #555;
}

.var-text {
  font-size: 11px;
  font-weight: bold;
  fill: white;
}

.var-value {
  font-size: 10px;
  fill: white;
}

.local-var {
  font-size: 9px;
  fill: #333;
  font-weight: bold;
}

.sync-title {
  font-size: 14px;
  font-weight: bold;
  fill: #2e7d32;
}

.sync-text {
  font-size: 10px;
  fill: #1b5e20;
  font-family: 'Courier New', monospace;
}

.flow-label {
  font-size: 10px;
  font-weight: bold;
  fill: #333;
}

.flow-label.volatile {
  fill: #f44336;
}

/* 动画效果 */
.highlight {
  stroke: #ff6f00 !important;
  stroke-width: 3px !important;
  animation: pulse 2s infinite;
}

.animate-in {
  animation: slideIn 1s ease-out;
}

.show {
  opacity: 1;
  animation: fadeIn 1s ease-in;
}

.show:not(.show) {
  opacity: 0;
}

.animate {
  animation: bounce 1s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% {
    stroke-opacity: 1;
  }
  50% {
    stroke-opacity: 0.5;
  }
  100% {
    stroke-opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounce {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(5px);
  }
}

.step-explanation {
  margin-top: 1rem;
}

.explanation-card {
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.explanation-card.info {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-left: 4px solid #2196f3;
}

.explanation-card.success {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  border-left: 4px solid #4caf50;
}

.explanation-card.warning {
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
  border-left: 4px solid #ff9800;
}

.explanation-card.danger {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  border-left: 4px solid #f44336;
}

.explanation-card h4 {
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-size: 1.2rem;
}

.explanation-card p {
  color: #555;
  line-height: 1.6;
  margin: 0;
}

@media (max-width: 768px) {
  .control-buttons {
    flex-direction: column;
    align-items: center;
  }

  .control-buttons button {
    width: 200px;
  }
}
</style>
