<template>
  <div v-if="isVisible" class="modal-overlay" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>{{ title }}</h3>
        <button @click="closeModal" class="close-button">✕</button>
      </div>
      <div class="modal-body">
        <component :is="getInteractiveComponent(type)" :data="data" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineComponent, h, ref } from 'vue'

interface Props {
  isVisible: boolean
  type: string
  title: string
  data?: any
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
}>()

const closeModal = () => {
  emit('close')
}

// 通用交互组件
const DefaultInteractive = defineComponent({
  name: 'DefaultInteractive',
  props: ['data'],
  setup(props) {
    return () =>
      h('div', { class: 'default-interactive' }, [
        h('h4', '🚧 功能开发中'),
        h('p', '这个交互功能正在开发中，敬请期待！'),
        h('div', { class: 'coming-soon' }, [
          h('div', { class: 'spinner' }),
          h('span', '即将推出...'),
        ]),
      ])
  },
})

const CodeComparison = defineComponent({
  name: 'CodeComparison',
  props: ['data'],
  setup(props) {
    const examples = ref([
      {
        title: '基础示例',
        before:
          '// 传统写法\nfor (int i = 0; i < list.size(); i++) {\n    System.out.println(list.get(i));\n}',
        after: '// 现代写法\nlist.forEach(System.out::println);',
      },
    ])

    const selectedExample = ref(0)

    return () =>
      h('div', { class: 'code-comparison' }, [
        h(
          'div',
          { class: 'example-tabs' },
          examples.value.map((example, index) =>
            h(
              'button',
              {
                key: index,
                onClick: () => (selectedExample.value = index),
                class: ['tab-button', { active: selectedExample.value === index }],
              },
              example.title,
            ),
          ),
        ),
        h('div', { class: 'comparison-content' }, [
          h('div', { class: 'code-section' }, [
            h('h4', '传统方式'),
            h('pre', { class: 'code-block' }, examples.value[selectedExample.value].before),
          ]),
          h('div', { class: 'code-section' }, [
            h('h4', '现代方式'),
            h('pre', { class: 'code-block' }, examples.value[selectedExample.value].after),
          ]),
        ]),
      ])
  },
})

const ConceptDemo = defineComponent({
  name: 'ConceptDemo',
  props: ['data'],
  setup(props) {
    const step = ref(0)
    const steps = ref([
      { title: '步骤 1', description: '初始化概念', code: 'System.out.println("Hello World");' },
      {
        title: '步骤 2',
        description: '应用概念',
        code: 'List<String> list = Arrays.asList("a", "b", "c");',
      },
      { title: '步骤 3', description: '验证结果', code: 'list.forEach(System.out::println);' },
    ])

    const nextStep = () => {
      if (step.value < steps.value.length - 1) {
        step.value++
      }
    }

    const prevStep = () => {
      if (step.value > 0) {
        step.value--
      }
    }

    const resetDemo = () => {
      step.value = 0
    }

    return () =>
      h('div', { class: 'concept-demo' }, [
        h('div', { class: 'demo-header' }, [
          h('h4', `${steps.value[step.value].title}: ${steps.value[step.value].description}`),
          h('div', { class: 'step-indicator' }, `${step.value + 1} / ${steps.value.length}`),
        ]),
        h('div', { class: 'demo-content' }, [
          h('pre', { class: 'code-block' }, steps.value[step.value].code),
        ]),
        h('div', { class: 'demo-controls' }, [
          h(
            'button',
            {
              onClick: prevStep,
              disabled: step.value === 0,
              class: 'control-button',
            },
            '⬅️ 上一步',
          ),
          h(
            'button',
            {
              onClick: resetDemo,
              class: 'control-button',
            },
            '🔄 重置',
          ),
          h(
            'button',
            {
              onClick: nextStep,
              disabled: step.value === steps.value.length - 1,
              class: 'control-button',
            },
            '下一步 ➡️',
          ),
        ]),
      ])
  },
})

const InteractivePlayground = defineComponent({
  name: 'InteractivePlayground',
  props: ['data'],
  setup(props) {
    const code = ref('// 在这里编写代码\nSystem.out.println("Hello, Interactive World!");')
    const output = ref('')
    const isRunning = ref(false)

    const runCode = async () => {
      isRunning.value = true
      output.value = '正在执行...\n'

      // 模拟代码执行
      await new Promise((resolve) => setTimeout(resolve, 1000))
      output.value = 'Hello, Interactive World!\n执行完成！'

      isRunning.value = false
    }

    return () =>
      h('div', { class: 'interactive-playground' }, [
        h('h4', '💻 交互式代码编辑器'),
        h('textarea', {
          value: code.value,
          onInput: (e: any) => (code.value = e.target.value),
          class: 'code-editor',
          rows: 8,
          placeholder: '在这里编写你的代码...',
        }),
        h(
          'button',
          {
            onClick: runCode,
            disabled: isRunning.value,
            class: 'run-button',
          },
          isRunning.value ? '⏳ 运行中...' : '▶️ 运行代码',
        ),
        h('div', { class: 'result-section' }, [
          h('h4', '📤 输出结果'),
          h('pre', { class: 'result-output' }, output.value),
        ]),
      ])
  },
})

// 第1章专用组件 - 编译过程演示
const CompilationDemo = defineComponent({
  name: 'CompilationDemo',
  props: ['data'],
  setup(props) {
    const step = ref(0)
    const steps = [
      {
        title: '📝 源代码',
        content: 'HelloWorld.java',
        description: '编写Java源代码文件，包含类定义和方法',
      },
      {
        title: '🔍 词法分析',
        content: 'javac 词法解析',
        description: '编译器将源代码分解为标记(tokens)',
      },
      {
        title: '🌳 语法分析',
        content: '构建语法树',
        description: '生成抽象语法树(AST)，表示代码结构',
      },
      { title: '✅ 语义分析', content: '类型检查', description: '验证类型安全性和语义正确性' },
      {
        title: '⚙️ 字节码生成',
        content: 'HelloWorld.class',
        description: '生成平台无关的字节码文件',
      },
      { title: '📦 类加载', content: 'ClassLoader 加载', description: 'JVM加载并验证字节码文件' },
      { title: '🚀 执行', content: 'JIT + 解释执行', description: '混合执行模式运行程序' },
    ]

    const nextStep = () => {
      if (step.value < steps.length - 1) step.value++
    }

    const prevStep = () => {
      if (step.value > 0) step.value--
    }

    const reset = () => {
      step.value = 0
    }

    return () =>
      h('div', { class: 'concept-demo' }, [
        h('div', { class: 'demo-header' }, [
          h('h4', '🔧 Java编译执行过程'),
          h('span', { class: 'step-indicator' }, `步骤 ${step.value + 1}/${steps.length}`),
        ]),
        h(
          'div',
          {
            class: 'compilation-step',
            style:
              'padding: 2rem; text-align: center; background: #f8f9fa; border-radius: 8px; margin: 1rem 0;',
          },
          [
            h('h3', { style: 'color: #667eea; margin-bottom: 1rem;' }, steps[step.value].title),
            h(
              'div',
              {
                style:
                  'font-size: 1.2rem; font-weight: bold; color: #333; margin: 1rem 0; padding: 1rem; background: white; border-radius: 6px; border: 2px solid #667eea;',
              },
              steps[step.value].content,
            ),
            h('p', { style: 'color: #666; line-height: 1.6;' }, steps[step.value].description),
          ],
        ),
        h('div', { class: 'demo-controls' }, [
          h(
            'button',
            {
              onClick: prevStep,
              disabled: step.value === 0,
              class: 'control-button',
            },
            '⬅️ 上一步',
          ),
          h(
            'button',
            {
              onClick: reset,
              class: 'control-button',
            },
            '🔄 重置',
          ),
          h(
            'button',
            {
              onClick: nextStep,
              disabled: step.value === steps.length - 1,
              class: 'control-button',
            },
            '下一步 ➡️',
          ),
        ]),
      ])
  },
})

// 第1章专用组件 - JVM架构图
const JVMArchitecture = defineComponent({
  name: 'JVMArchitecture',
  props: ['data'],
  setup(props) {
    const selectedArea = ref('class-loader')
    const areas = {
      'class-loader': {
        title: '🔧 类加载器 (ClassLoader)',
        description:
          '负责加载.class文件到JVM内存中，包括Bootstrap、Extension、Application三级加载器，确保类的安全加载和命名空间隔离。',
      },
      'runtime-data': {
        title: '💾 运行时数据区 (Runtime Data Areas)',
        description:
          '包括方法区(存储类信息)、堆内存(对象存储)、栈内存(方法调用)、PC寄存器(程序计数器)、本地方法栈等内存区域。',
      },
      'execution-engine': {
        title: '⚡ 执行引擎 (Execution Engine)',
        description:
          '负责执行字节码，包括解释器(逐行解释)、JIT编译器(热点代码编译)、垃圾收集器(内存管理)等核心组件。',
      },
    }

    const selectArea = (area: string) => {
      selectedArea.value = area
    }

    return () =>
      h('div', { class: 'concept-demo' }, [
        h('h4', '🏗️ JVM架构组件'),
        h(
          'div',
          {
            class: 'jvm-areas',
            style:
              'display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin: 1rem 0;',
          },
          [
            Object.entries(areas).map(([key, area]) =>
              h(
                'div',
                {
                  class: 'jvm-area',
                  style: `padding: 1rem; border: 2px solid ${selectedArea.value === key ? '#667eea' : '#e9ecef'}; border-radius: 8px; cursor: pointer; transition: all 0.2s ease; background: ${selectedArea.value === key ? '#f0f4ff' : 'white'};`,
                  onClick: () => selectArea(key),
                },
                [
                  h('h5', { style: 'margin: 0 0 0.5rem 0; color: #333;' }, area.title),
                  h(
                    'p',
                    { style: 'margin: 0; color: #666; font-size: 0.9rem; line-height: 1.4;' },
                    area.description,
                  ),
                ],
              ),
            ),
          ],
        ),
        selectedArea.value &&
          h(
            'div',
            {
              class: 'area-details',
              style:
                'margin-top: 1rem; padding: 1.5rem; background: #667eea; color: white; border-radius: 8px;',
            },
            [
              h(
                'h4',
                { style: 'margin: 0 0 1rem 0;' },
                `详细说明: ${areas[selectedArea.value as keyof typeof areas].title}`,
              ),
              h(
                'p',
                { style: 'margin: 0; line-height: 1.6;' },
                areas[selectedArea.value as keyof typeof areas].description,
              ),
            ],
          ),
      ])
  },
})

// 第1章专用组件 - var类型推断演示
const VarTypeInference = defineComponent({
  name: 'VarTypeInference',
  props: ['data'],
  setup(props) {
    const currentExample = ref(0)
    const examples = [
      {
        title: '基本类型推断',
        code: `// 传统方式
String message = "Hello, Java!";
List<String> names = new ArrayList<>();
Map<String, Integer> scores = new HashMap<>();

// 使用var简化
var message = "Hello, Java!";        // 推断为 String
var names = new ArrayList<String>(); // 推断为 ArrayList<String>
var scores = new HashMap<String, Integer>(); // 推断为 HashMap<String, Integer>`,
        explanation: 'var关键字让编译器根据初始化表达式自动推断变量类型，减少代码冗余',
      },
      {
        title: '复杂泛型简化',
        code: `// 传统方式 - 冗长的泛型声明
Map<String, List<Map<String, Object>>> complexData =
    new HashMap<String, List<Map<String, Object>>>();

// 使用var简化
var complexData = new HashMap<String, List<Map<String, Object>>>();

// 方法链调用
var result = someService.getData()
    .filter(item -> item.isValid())
    .map(item -> item.getName())
    .collect(Collectors.toList());`,
        explanation: '在复杂泛型和方法链场景中，var显著提升代码可读性',
      },
      {
        title: '使用限制示例',
        code: `// ❌ 错误用法
// var field;                    // 不能用于字段
// public void method(var param) // 不能用于参数
// var x;                        // 必须初始化
// var y = null;                 // 无法推断类型

// ✅ 正确用法
var text = "Hello";              // String
var number = 42;                 // int
var list = List.of(1, 2, 3);     // List<Integer>
var optional = Optional.empty(); // Optional<Object>`,
        explanation: 'var只能用于局部变量，且必须在声明时初始化',
      },
    ]

    const nextExample = () => {
      currentExample.value = (currentExample.value + 1) % examples.length
    }

    const prevExample = () => {
      currentExample.value =
        currentExample.value === 0 ? examples.length - 1 : currentExample.value - 1
    }

    return () =>
      h('div', { class: 'interactive-playground' }, [
        h('div', { class: 'demo-header' }, [
          h('h4', '🧠 var类型推断演示'),
          h(
            'span',
            { class: 'step-indicator' },
            `示例 ${currentExample.value + 1}/${examples.length}`,
          ),
        ]),
        h('h3', { style: 'color: #667eea; margin: 1rem 0;' }, examples[currentExample.value].title),
        h(
          'pre',
          {
            class: 'code-block',
            style:
              'background: #2d3748; color: #e2e8f0; padding: 1.5rem; border-radius: 8px; overflow-x: auto; line-height: 1.6;',
          },
          examples[currentExample.value].code,
        ),
        h(
          'div',
          {
            style:
              'background: #f0f4ff; padding: 1rem; border-radius: 6px; border-left: 4px solid #667eea; margin: 1rem 0;',
          },
          [
            h(
              'p',
              { style: 'margin: 0; color: #333; line-height: 1.6;' },
              examples[currentExample.value].explanation,
            ),
          ],
        ),
        h('div', { class: 'demo-controls' }, [
          h(
            'button',
            {
              onClick: prevExample,
              class: 'control-button',
            },
            '⬅️ 上一个示例',
          ),
          h(
            'button',
            {
              onClick: nextExample,
              class: 'control-button',
            },
            '下一个示例 ➡️',
          ),
        ]),
      ])
  },
})

// 第1章专用组件 - 集合工厂演示
const CollectionFactoryDemo = defineComponent({
  name: 'CollectionFactoryDemo',
  props: ['data'],
  setup(props) {
    const activeTab = ref(0)
    const examples = [
      {
        title: 'List.of() 示例',
        oldWay: `// 传统方式创建不可变列表
List<String> fruits = Collections.unmodifiableList(
    Arrays.asList("apple", "banana", "cherry")
);

// 或者使用 ArrayList
List<String> fruits = new ArrayList<>();
fruits.add("apple");
fruits.add("banana");
fruits.add("cherry");
fruits = Collections.unmodifiableList(fruits);`,
        newWay: `// Java 11 集合工厂方法
var fruits = List.of("apple", "banana", "cherry");

// 简洁、不可变、类型安全
System.out.println(fruits.size()); // 3
System.out.println(fruits.get(0)); // apple

// fruits.add("orange"); // 抛出 UnsupportedOperationException`,
        benefits: ['代码更简洁', '自动类型推断', '真正的不可变性', '更好的性能'],
      },
      {
        title: 'Set.of() 示例',
        oldWay: `// 传统方式创建不可变集合
Set<Integer> numbers = Collections.unmodifiableSet(
    new HashSet<>(Arrays.asList(1, 2, 3, 4, 5))
);

// 或者手动添加
Set<Integer> numbers = new HashSet<>();
numbers.add(1);
numbers.add(2);
numbers.add(3);
numbers = Collections.unmodifiableSet(numbers);`,
        newWay: `// Java 11 集合工厂方法
var numbers = Set.of(1, 2, 3, 4, 5);

// 自动去重、不可变
System.out.println(numbers.contains(3)); // true
System.out.println(numbers.size()); // 5

// numbers.add(6); // 抛出 UnsupportedOperationException`,
        benefits: ['自动去重', '不可变性保证', '内存优化', '线程安全'],
      },
      {
        title: 'Map.of() 示例',
        oldWay: `// 传统方式创建不可变映射
Map<String, Integer> config = new HashMap<>();
config.put("timeout", 30000);
config.put("retries", 3);
config.put("port", 8080);
config = Collections.unmodifiableMap(config);`,
        newWay: `// Java 11 集合工厂方法
var config = Map.of(
    "timeout", 30000,
    "retries", 3,
    "port", 8080
);

// 简洁的键值对定义
System.out.println(config.get("timeout")); // 30000

// config.put("ssl", true); // 抛出 UnsupportedOperationException`,
        benefits: ['键值对语法简洁', '编译时类型检查', '不可变性', '适合配置常量'],
      },
    ]

    const switchTab = (index: number) => {
      activeTab.value = index
    }

    return () =>
      h('div', { class: 'code-comparison' }, [
        h('h4', '🏭 集合工厂方法演示'),
        h('div', { class: 'example-tabs' }, [
          examples.map((example, index) =>
            h(
              'button',
              {
                class: ['tab-button', activeTab.value === index ? 'active' : ''],
                onClick: () => switchTab(index),
              },
              example.title,
            ),
          ),
        ]),
        h('div', { class: 'comparison-content' }, [
          h('div', { class: 'code-section' }, [
            h('h4', '❌ 传统方式'),
            h('pre', { class: 'code-block' }, examples[activeTab.value].oldWay),
          ]),
          h('div', { class: 'code-section' }, [
            h('h4', '✅ 集合工厂方法'),
            h('pre', { class: 'code-block' }, examples[activeTab.value].newWay),
          ]),
        ]),
        h(
          'div',
          {
            style: 'margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 6px;',
          },
          [
            h('h4', { style: 'margin: 0 0 0.5rem 0; color: #333;' }, '✨ 主要优势'),
            h('ul', { style: 'margin: 0; padding-left: 1.5rem;' }, [
              examples[activeTab.value].benefits.map((benefit) =>
                h('li', { style: 'color: #666; margin: 0.25rem 0;' }, benefit),
              ),
            ]),
          ],
        ),
      ])
  },
})

// 第1章专用组件 - 字节码分析
const BytecodeAnalysis = defineComponent({
  name: 'BytecodeAnalysis',
  props: ['data'],
  setup(props) {
    const activeExample = ref(0)
    const examples = [
      {
        title: 'HelloWorld.java',
        javaCode: `public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}`,
        bytecode: `public class HelloWorld
  minor version: 0
  major version: 61
  flags: (0x0021) ACC_PUBLIC, ACC_SUPER
  this_class: #7                          // HelloWorld
  super_class: #2                         // java/lang/Object
  interfaces: 0, fields: 0, methods: 2, attributes: 1
Constant pool:
   #1 = Methodref          #2.#3          // java/lang/Object."<init>":()V
   #2 = Class              #4             // java/lang/Object
   #3 = NameAndType        #5:#6          // "<init>":()V
   #4 = Utf8               java/lang/Object
   #5 = Utf8               <init>
   #6 = Utf8               ()V
   #7 = Class              #8             // HelloWorld
   #8 = Utf8               HelloWorld
   #9 = Methodref          #10.#11        // java/io/PrintStream.println:(Ljava/lang/String;)V
  #10 = Class              #12            // java/io/PrintStream
  #11 = NameAndType        #13:#14        // println:(Ljava/lang/String;)V
  #12 = Utf8               java/io/PrintStream
  #13 = Utf8               println
  #14 = Utf8               (Ljava/lang/String;)V
  #15 = Fieldref           #16.#17        // java/lang/System.out:Ljava/io/PrintStream;
  #16 = Class              #18            // java/lang/System
  #17 = NameAndType        #19:#20        // out:Ljava/io/PrintStream;
  #18 = Utf8               java/lang/System
  #19 = Utf8               out
  #20 = Utf8               Ljava/io/PrintStream;
  #21 = String             #22            // Hello, World!
  #22 = Utf8               Hello, World!

{
  public HelloWorld();
    descriptor: ()V
    flags: (0x0001) ACC_PUBLIC
    Code:
      stack=1, locals=1, args_size=1
         0: aload_0
         1: invokespecial #1                  // Method java/lang/Object."<init>":()V
         4: return

  public static void main(java.lang.String[]);
    descriptor: ([Ljava/lang/String;)V
    flags: (0x0009) ACC_PUBLIC, ACC_STATIC
    Code:
      stack=2, locals=1, args_size=1
         0: getstatic     #15                 // Field java/lang/System.out:Ljava/io/PrintStream;
         3: ldc           #21                 // String Hello, World!
         5: invokevirtual #9                  // Method java/io/PrintStream.println:(Ljava/lang/String;)V
         8: return
}`,
        explanation:
          '这是最简单的Java程序的字节码。注意常量池如何存储所有的字符串和方法引用，以及main方法的字节码指令序列。',
      },
      {
        title: 'var关键字示例',
        javaCode: `public class VarExample {
    public static void main(String[] args) {
        var message = "Hello";
        var number = 42;
        var list = List.of(1, 2, 3);
        System.out.println(message + number);
    }
}`,
        bytecode: `// 编译后的字节码显示var被替换为具体类型
public static void main(java.lang.String[]);
  descriptor: ([Ljava/lang/String;)V
  flags: (0x0009) ACC_PUBLIC, ACC_STATIC
  Code:
    stack=3, locals=4, args_size=1
       0: ldc           #7                  // String Hello
       2: astore_1                          // String message
       3: bipush        42
       5: istore_2                          // int number
       6: iconst_3
       7: anewarray     #9                  // class java/lang/Integer
      10: dup
      11: iconst_0
      12: bipush        1
      14: invokestatic  #11                 // Method java/lang/Integer.valueOf:(I)Ljava/lang/Integer;
      17: aastore
      18: dup
      19: iconst_1
      20: bipush        2
      22: invokestatic  #11                 // Method java/lang/Integer.valueOf:(I)Ljava/lang/Integer;
      25: aastore
      26: dup
      27: iconst_2
      28: bipush        3
      30: invokestatic  #11                 // Method java/lang/Integer.valueOf:(I)Ljava/lang/Integer;
      33: aastore
      34: invokestatic  #17                 // Method java/util/List.of:([Ljava/lang/Object;)Ljava/util/List;
      37: astore_3                          // List list
      38: getstatic     #21                 // Field java/lang/System.out:Ljava/io/PrintStream;
      41: new           #27                 // class java/lang/StringBuilder
      44: dup
      45: invokespecial #29                 // Method java/lang/StringBuilder."<init>":()V
      48: aload_1
      49: invokevirtual #30                 // Method java/lang/StringBuilder.append:(Ljava/lang/String;)Ljava/lang/StringBuilder;
      52: iload_2
      53: invokevirtual #34                 // Method java/lang/StringBuilder.append:(I)Ljava/lang/StringBuilder;
      56: invokevirtual #37                 // Method java/lang/StringBuilder.toString:()Ljava/lang/String;
      59: invokevirtual #41                 // Method java/io/PrintStream.println:(Ljava/lang/String;)V
      62: return`,
        explanation:
          '注意var关键字在字节码中完全消失了，编译器将其替换为具体的类型。这证明var是编译时特性，不影响运行时性能。',
      },
    ]

    const switchExample = (index: number) => {
      activeExample.value = index
    }

    return () =>
      h('div', { class: 'code-comparison' }, [
        h('h4', '🔍 Java字节码分析'),
        h('div', { class: 'example-tabs' }, [
          examples.map((example, index) =>
            h(
              'button',
              {
                class: ['tab-button', activeExample.value === index ? 'active' : ''],
                onClick: () => switchExample(index),
              },
              example.title,
            ),
          ),
        ]),
        h('div', { class: 'comparison-content' }, [
          h('div', { class: 'code-section' }, [
            h('h4', '☕ Java源代码'),
            h(
              'pre',
              {
                class: 'code-block',
                style: 'background: #f8f9fa; color: #333; max-height: 300px; overflow-y: auto;',
              },
              examples[activeExample.value].javaCode,
            ),
          ]),
          h('div', { class: 'code-section' }, [
            h('h4', '⚙️ 字节码 (javap -c -v)'),
            h(
              'pre',
              {
                class: 'code-block',
                style:
                  'background: #2d3748; color: #e2e8f0; max-height: 300px; overflow-y: auto; font-size: 0.75rem;',
              },
              examples[activeExample.value].bytecode,
            ),
          ]),
        ]),
        h(
          'div',
          {
            style:
              'margin-top: 1rem; padding: 1rem; background: #e8f4fd; border-radius: 6px; border-left: 4px solid #2196f3;',
          },
          [
            h('h4', { style: 'margin: 0 0 0.5rem 0; color: #1976d2;' }, '💡 分析要点'),
            h(
              'p',
              { style: 'margin: 0; color: #333; line-height: 1.6;' },
              examples[activeExample.value].explanation,
            ),
          ],
        ),
      ])
  },
})

// 第1章专用组件 - 发布时间线
const ReleaseTimeline = defineComponent({
  name: 'ReleaseTimeline',
  props: ['data'],
  setup(props) {
    const selectedRelease = ref(null)
    const releases = [
      {
        version: 'Java 8',
        date: '2014年3月',
        type: 'LTS',
        features: ['Lambda表达式', 'Stream API', '接口默认方法', '新日期时间API'],
        description: '现代Java的起点，引入函数式编程',
      },
      {
        version: 'Java 9',
        date: '2017年9月',
        type: 'Feature',
        features: ['模块系统(Jigsaw)', 'JShell', '集合工厂方法'],
        description: '新发布模型的第一个版本',
      },
      {
        version: 'Java 10',
        date: '2018年3月',
        type: 'Feature',
        features: ['var关键字', '应用类数据共享', '并行Full GC'],
        description: '6个月发布周期开始',
      },
      {
        version: 'Java 11',
        date: '2018年9月',
        type: 'LTS',
        features: ['HTTP/2客户端', '单文件程序', 'String新方法'],
        description: '新模型下的第一个LTS版本',
      },
      {
        version: 'Java 12',
        date: '2019年3月',
        type: 'Feature',
        features: ['Switch表达式(预览)', 'Shenandoah GC'],
        description: '预览特性机制展示',
      },
      {
        version: 'Java 13',
        date: '2019年9月',
        type: 'Feature',
        features: ['文本块(预览)', 'Switch表达式改进'],
        description: '多行字符串支持',
      },
      {
        version: 'Java 14',
        date: '2020年3月',
        type: 'Feature',
        features: ['Switch表达式(正式)', 'Records(预览)', 'instanceof模式匹配(预览)'],
        description: 'Switch表达式正式发布',
      },
      {
        version: 'Java 15',
        date: '2020年9月',
        type: 'Feature',
        features: ['文本块(正式)', 'Sealed Classes(预览)'],
        description: '文本块正式发布',
      },
      {
        version: 'Java 16',
        date: '2021年3月',
        type: 'Feature',
        features: ['Records(正式)', 'instanceof模式匹配(正式)'],
        description: 'Records正式发布',
      },
      {
        version: 'Java 17',
        date: '2021年9月',
        type: 'LTS',
        features: ['Sealed Classes(正式)', '新的macOS渲染管道'],
        description: '当前最新LTS版本',
      },
      {
        version: 'Java 18',
        date: '2022年3月',
        type: 'Feature',
        features: ['UTF-8默认字符集', '简单Web服务器'],
        description: '默认字符集统一',
      },
      {
        version: 'Java 19',
        date: '2022年9月',
        type: 'Feature',
        features: ['虚拟线程(预览)', 'Pattern Matching for switch(预览)'],
        description: '虚拟线程引入',
      },
      {
        version: 'Java 20',
        date: '2023年3月',
        type: 'Feature',
        features: ['Scoped Values(孵化)', 'Record Patterns(预览)'],
        description: '作用域值概念',
      },
      {
        version: 'Java 21',
        date: '2023年9月',
        type: 'LTS',
        features: ['虚拟线程(正式)', 'Pattern Matching for switch(正式)'],
        description: '下一个LTS版本',
      },
    ]

    const selectRelease = (release: any) => {
      selectedRelease.value = selectedRelease.value === release ? null : release
    }

    return () =>
      h('div', { class: 'concept-demo' }, [
        h('h4', '📅 Java发布时间线'),
        h(
          'div',
          {
            style:
              'max-height: 400px; overflow-y: auto; padding: 1rem; background: #f8f9fa; border-radius: 8px;',
          },
          [
            h('div', { style: 'position: relative;' }, [
              // 时间线主线
              h('div', {
                style:
                  'position: absolute; left: 20px; top: 0; bottom: 0; width: 2px; background: #667eea;',
              }),
              // 发布节点
              releases.map((release, index) =>
                h(
                  'div',
                  {
                    style:
                      'position: relative; margin-bottom: 1.5rem; padding-left: 50px; cursor: pointer;',
                    onClick: () => selectRelease(release),
                  },
                  [
                    // 节点圆点
                    h('div', {
                      style: `position: absolute; left: 11px; top: 5px; width: 18px; height: 18px; border-radius: 50%; background: ${release.type === 'LTS' ? '#4caf50' : '#ff9800'}; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);`,
                    }),
                    // 发布信息
                    h(
                      'div',
                      {
                        style: `padding: 0.75rem; background: white; border-radius: 6px; border-left: 4px solid ${release.type === 'LTS' ? '#4caf50' : '#ff9800'}; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: all 0.2s ease;`,
                        onMouseover: (e: any) => (e.target.style.transform = 'translateX(5px)'),
                        onMouseleave: (e: any) => (e.target.style.transform = 'translateX(0)'),
                      },
                      [
                        h(
                          'div',
                          {
                            style:
                              'display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;',
                          },
                          [
                            h('h5', { style: 'margin: 0; color: #333;' }, release.version),
                            h(
                              'span',
                              {
                                style: `padding: 0.25rem 0.5rem; border-radius: 12px; font-size: 0.75rem; background: ${release.type === 'LTS' ? '#e8f5e8' : '#fff3e0'}; color: ${release.type === 'LTS' ? '#2e7d32' : '#f57c00'};`,
                              },
                              release.type,
                            ),
                          ],
                        ),
                        h(
                          'p',
                          { style: 'margin: 0 0 0.5rem 0; color: #666; font-size: 0.9rem;' },
                          `${release.date} - ${release.description}`,
                        ),
                        selectedRelease.value === release &&
                          h('div', { style: 'margin-top: 0.5rem;' }, [
                            h('h6', { style: 'margin: 0 0 0.5rem 0; color: #333;' }, '主要特性:'),
                            h('ul', { style: 'margin: 0; padding-left: 1.5rem;' }, [
                              release.features.map((feature) =>
                                h(
                                  'li',
                                  { style: 'color: #555; margin: 0.25rem 0; font-size: 0.9rem;' },
                                  feature,
                                ),
                              ),
                            ]),
                          ]),
                      ],
                    ),
                  ],
                ),
              ),
            ]),
          ],
        ),
        h(
          'div',
          {
            style: 'margin-top: 1rem; padding: 1rem; background: #e3f2fd; border-radius: 6px;',
          },
          [
            h('div', { style: 'display: flex; gap: 2rem; justify-content: center;' }, [
              h('div', { style: 'display: flex; align-items: center; gap: 0.5rem;' }, [
                h('div', {
                  style: 'width: 12px; height: 12px; border-radius: 50%; background: #4caf50;',
                }),
                h('span', { style: 'font-size: 0.9rem; color: #333;' }, 'LTS版本 (长期支持)'),
              ]),
              h('div', { style: 'display: flex; align-items: center; gap: 0.5rem;' }, [
                h('div', {
                  style: 'width: 12px; height: 12px; border-radius: 50%; background: #ff9800;',
                }),
                h('span', { style: 'font-size: 0.9rem; color: #333;' }, 'Feature版本 (6个月)'),
              ]),
            ]),
          ],
        ),
      ])
  },
})

// 第1章专用组件 - LTS版本对比
const LTSComparison = defineComponent({
  name: 'LTSComparison',
  props: ['data'],
  setup(props) {
    const selectedComparison = ref('features')
    const comparisons = {
      features: {
        title: '主要特性对比',
        data: [
          {
            feature: 'Lambda表达式',
            java8: '✅ 首次引入',
            java11: '✅ 成熟稳定',
            java17: '✅ 持续优化',
            java21: '✅ 性能提升',
          },
          {
            feature: 'Stream API',
            java8: '✅ 基础版本',
            java11: '✅ 增强功能',
            java17: '✅ 性能改进',
            java21: '✅ 新增方法',
          },
          {
            feature: '模块系统',
            java8: '❌ 不支持',
            java11: '✅ 完整支持',
            java17: '✅ 稳定版本',
            java21: '✅ 持续改进',
          },
          {
            feature: 'var关键字',
            java8: '❌ 不支持',
            java11: '✅ 局部变量',
            java17: '✅ 稳定使用',
            java21: '✅ 扩展支持',
          },
          {
            feature: 'HTTP/2客户端',
            java8: '❌ 不支持',
            java11: '✅ 内置支持',
            java17: '✅ 性能优化',
            java21: '✅ 功能增强',
          },
          {
            feature: 'Records',
            java8: '❌ 不支持',
            java11: '❌ 不支持',
            java17: '✅ 正式版本',
            java21: '✅ 模式匹配',
          },
          {
            feature: 'Sealed Classes',
            java8: '❌ 不支持',
            java11: '❌ 不支持',
            java17: '✅ 正式版本',
            java21: '✅ 增强功能',
          },
          {
            feature: '虚拟线程',
            java8: '❌ 不支持',
            java11: '❌ 不支持',
            java17: '❌ 不支持',
            java21: '✅ 正式版本',
          },
        ],
      },
      performance: {
        title: '性能与内存对比',
        data: [
          {
            metric: '启动时间',
            java8: '基准 (100%)',
            java11: '改进 (~85%)',
            java17: '优化 (~75%)',
            java21: '显著提升 (~60%)',
          },
          {
            metric: '内存占用',
            java8: '基准',
            java11: '减少 ~10%',
            java17: '减少 ~15%',
            java21: '减少 ~25%',
          },
          {
            metric: 'GC性能',
            java8: 'G1GC可选',
            java11: 'G1GC默认',
            java17: 'ZGC可用',
            java21: 'ZGC生产就绪',
          },
          {
            metric: 'JIT编译',
            java8: 'C1/C2',
            java11: 'C1/C2优化',
            java17: 'Graal可选',
            java21: 'Graal增强',
          },
          {
            metric: '容器支持',
            java8: '有限支持',
            java11: '基础支持',
            java17: '良好支持',
            java21: '原生优化',
          },
        ],
      },
      ecosystem: {
        title: '生态系统支持',
        data: [
          {
            aspect: '框架支持',
            java8: 'Spring 4.x',
            java11: 'Spring 5.x',
            java17: 'Spring 6.x',
            java21: 'Spring 6.x+',
          },
          {
            aspect: '构建工具',
            java8: 'Maven/Gradle',
            java11: 'Maven/Gradle',
            java17: 'Maven/Gradle',
            java21: 'Maven/Gradle',
          },
          {
            aspect: 'IDE支持',
            java8: '完整支持',
            java11: '完整支持',
            java17: '完整支持',
            java21: '完整支持',
          },
          {
            aspect: '云平台',
            java8: '广泛支持',
            java11: '广泛支持',
            java17: '优先支持',
            java21: '最新支持',
          },
          {
            aspect: '企业采用',
            java8: '主流选择',
            java11: '逐步迁移',
            java17: '新项目首选',
            java21: '前沿项目',
          },
        ],
      },
    }

    const switchComparison = (type: string) => {
      selectedComparison.value = type
    }

    return () =>
      h('div', { class: 'code-comparison' }, [
        h('h4', '📊 LTS版本全面对比'),
        h('div', { class: 'example-tabs' }, [
          Object.entries(comparisons).map(([key, comp]) =>
            h(
              'button',
              {
                class: ['tab-button', selectedComparison.value === key ? 'active' : ''],
                onClick: () => switchComparison(key),
              },
              comp.title,
            ),
          ),
        ]),
        h('div', { style: 'margin-top: 1rem;' }, [
          h(
            'div',
            {
              style:
                'overflow-x: auto; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);',
            },
            [
              h(
                'table',
                {
                  style: 'width: 100%; border-collapse: collapse; min-width: 600px;',
                },
                [
                  h('thead', [
                    h('tr', { style: 'background: #667eea; color: white;' }, [
                      h(
                        'th',
                        {
                          style:
                            'padding: 1rem; text-align: left; border-right: 1px solid rgba(255,255,255,0.2);',
                        },
                        selectedComparison.value === 'features'
                          ? '特性'
                          : selectedComparison.value === 'performance'
                            ? '性能指标'
                            : '生态方面',
                      ),
                      h(
                        'th',
                        {
                          style:
                            'padding: 1rem; text-align: center; border-right: 1px solid rgba(255,255,255,0.2);',
                        },
                        'Java 8 LTS',
                      ),
                      h(
                        'th',
                        {
                          style:
                            'padding: 1rem; text-align: center; border-right: 1px solid rgba(255,255,255,0.2);',
                        },
                        'Java 11 LTS',
                      ),
                      h(
                        'th',
                        {
                          style:
                            'padding: 1rem; text-align: center; border-right: 1px solid rgba(255,255,255,0.2);',
                        },
                        'Java 17 LTS',
                      ),
                      h('th', { style: 'padding: 1rem; text-align: center;' }, 'Java 21 LTS'),
                    ]),
                  ]),
                  h('tbody', [
                    comparisons[selectedComparison.value as keyof typeof comparisons].data.map(
                      (row: any, index: number) =>
                        h(
                          'tr',
                          {
                            style: `background: ${index % 2 === 0 ? '#f8f9fa' : 'white'}; border-bottom: 1px solid #e9ecef;`,
                          },
                          [
                            h(
                              'td',
                              {
                                style:
                                  'padding: 0.75rem; font-weight: 500; color: #333; border-right: 1px solid #e9ecef;',
                              },
                              row.feature || row.metric || row.aspect,
                            ),
                            h(
                              'td',
                              {
                                style:
                                  'padding: 0.75rem; text-align: center; border-right: 1px solid #e9ecef; font-size: 0.9rem;',
                              },
                              row.java8,
                            ),
                            h(
                              'td',
                              {
                                style:
                                  'padding: 0.75rem; text-align: center; border-right: 1px solid #e9ecef; font-size: 0.9rem;',
                              },
                              row.java11,
                            ),
                            h(
                              'td',
                              {
                                style:
                                  'padding: 0.75rem; text-align: center; border-right: 1px solid #e9ecef; font-size: 0.9rem;',
                              },
                              row.java17,
                            ),
                            h(
                              'td',
                              { style: 'padding: 0.75rem; text-align: center; font-size: 0.9rem;' },
                              row.java21,
                            ),
                          ],
                        ),
                    ),
                  ]),
                ],
              ),
            ],
          ),
        ]),
        h(
          'div',
          {
            style:
              'margin-top: 1rem; padding: 1rem; background: #f0f8ff; border-radius: 6px; border-left: 4px solid #2196f3;',
          },
          [
            h('h4', { style: 'margin: 0 0 0.5rem 0; color: #1976d2;' }, '💡 选择建议'),
            h('ul', { style: 'margin: 0; padding-left: 1.5rem; color: #333;' }, [
              h('li', { style: 'margin: 0.5rem 0;' }, '新项目推荐：Java 17 或 Java 21'),
              h('li', { style: 'margin: 0.5rem 0;' }, '稳定性优先：Java 11 或 Java 17'),
              h(
                'li',
                { style: 'margin: 0.5rem 0;' },
                '遗留系统：可继续使用 Java 8，但建议制定迁移计划',
              ),
              h('li', { style: 'margin: 0.5rem 0;' }, '性能敏感：Java 21 提供最佳性能和内存效率'),
            ]),
          ],
        ),
      ])
  },
})

// 第1章专用组件 - var使用示例
const VarExamples = defineComponent({
  name: 'VarExamples',
  props: ['data'],
  setup(props) {
    const activeCategory = ref(0)
    const categories = [
      {
        title: '✅ 推荐用法',
        examples: [
          {
            title: '复杂泛型简化',
            code: `// 传统方式 - 冗长且容易出错
Map<String, List<Map<String, Object>>> complexData =
    new HashMap<String, List<Map<String, Object>>>();

// 使用var - 简洁明了
var complexData = new HashMap<String, List<Map<String, Object>>>();`,
            explanation: '在复杂泛型类型中，var显著提升代码可读性',
          },
          {
            title: '方法链调用',
            code: `// 传统方式 - 类型声明冗长
Stream<String> filteredNames = users.stream()
    .filter(user -> user.isActive())
    .map(User::getName);

// 使用var - 关注逻辑而非类型
var filteredNames = users.stream()
    .filter(user -> user.isActive())
    .map(User::getName);`,
            explanation: '在方法链中，var让代码更关注业务逻辑',
          },
          {
            title: '局部变量初始化',
            code: `// 传统方式
DatabaseConnection connection = connectionFactory.createConnection();
UserRepository repository = new UserRepository(connection);
List<User> activeUsers = repository.findActiveUsers();

// 使用var - 减少重复
var connection = connectionFactory.createConnection();
var repository = new UserRepository(connection);
var activeUsers = repository.findActiveUsers();`,
            explanation: '在局部变量初始化中，var减少类型重复声明',
          },
        ],
      },
      {
        title: '❌ 不推荐用法',
        examples: [
          {
            title: '类型不明确的情况',
            code: `// 不好的例子 - 类型不明确
var data = getData();  // 返回类型不明确
var result = process(data);  // 难以理解

// 更好的方式 - 明确类型意图
UserData data = getData();
ProcessResult result = process(data);`,
            explanation: '当方法名不能清楚表达返回类型时，应避免使用var',
          },
          {
            title: '基本类型的简单赋值',
            code: `// 不必要的使用
var count = 0;        // int count = 0; 更清晰
var flag = true;      // boolean flag = true; 更明确
var name = "John";    // String name = "John"; 更直观

// 推荐的使用场景
var count = calculateTotalCount();  // 方法调用
var flag = isValidUser(user);       // 方法调用
var name = user.getFullName();      // 方法调用`,
            explanation: '对于简单的字面量赋值，显式类型声明更清晰',
          },
          {
            title: '可能导致混淆的情况',
            code: `// 容易混淆的例子
var list = new ArrayList<>();  // 泛型类型丢失
var map = new HashMap<>();     // 键值类型不明确

// 更好的方式
var list = new ArrayList<String>();
var map = new HashMap<String, Integer>();
// 或者
List<String> list = new ArrayList<>();
Map<String, Integer> map = new HashMap<>();`,
            explanation: '确保var不会丢失重要的类型信息',
          },
        ],
      },
    ]

    const switchCategory = (index: number) => {
      activeCategory.value = index
    }

    return () =>
      h('div', { class: 'interactive-playground' }, [
        h('h4', '💡 var关键字使用示例'),
        h('div', { class: 'example-tabs' }, [
          categories.map((category, index) =>
            h(
              'button',
              {
                class: ['tab-button', activeCategory.value === index ? 'active' : ''],
                onClick: () => switchCategory(index),
              },
              category.title,
            ),
          ),
        ]),
        h('div', { style: 'margin-top: 1rem;' }, [
          categories[activeCategory.value].examples.map((example, index) =>
            h(
              'div',
              {
                style:
                  'margin-bottom: 2rem; padding: 1.5rem; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);',
              },
              [
                h(
                  'h5',
                  {
                    style:
                      'margin: 0 0 1rem 0; color: #333; display: flex; align-items: center; gap: 0.5rem;',
                  },
                  [
                    h(
                      'span',
                      { style: `color: ${activeCategory.value === 0 ? '#4caf50' : '#f44336'};` },
                      activeCategory.value === 0 ? '✅' : '❌',
                    ),
                    example.title,
                  ],
                ),
                h(
                  'pre',
                  {
                    class: 'code-block',
                    style:
                      'background: #2d3748; color: #e2e8f0; padding: 1.5rem; border-radius: 6px; overflow-x: auto; line-height: 1.6; margin: 1rem 0;',
                  },
                  example.code,
                ),
                h(
                  'div',
                  {
                    style: `padding: 1rem; border-radius: 6px; border-left: 4px solid ${activeCategory.value === 0 ? '#4caf50' : '#f44336'}; background: ${activeCategory.value === 0 ? '#f1f8e9' : '#ffebee'};`,
                  },
                  [
                    h(
                      'p',
                      { style: 'margin: 0; color: #333; line-height: 1.6;' },
                      example.explanation,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ]),
      ])
  },
})

// 第1章专用组件 - 知识测验
const KnowledgeQuiz = defineComponent({
  name: 'KnowledgeQuiz',
  props: ['data'],
  setup(props) {
    const currentQuestion = ref(0)
    const selectedAnswers = ref<number[]>([])
    const showResults = ref(false)
    const questions = [
      {
        question: 'Java语言和Java平台的主要区别是什么？',
        options: [
          'Java语言是编程语言，Java平台是运行环境',
          'Java语言包含JVM，Java平台只是类库',
          'Java语言是源代码，Java平台是字节码',
          '没有区别，它们是同一个概念',
        ],
        correct: 0,
        explanation:
          'Java语言是静态类型的编程语言，而Java平台是提供运行环境的软件集合，包括JVM、类库和工具。',
      },
      {
        question: '新的Java发布模型的主要特点是什么？',
        options: [
          '每年发布一个版本',
          '每6个月发布一个版本，时间驱动',
          '等待重大特性完成后发布',
          '不定期发布',
        ],
        correct: 1,
        explanation: '新的发布模型采用时间驱动方式，每6个月固定发布新版本，不再等待重大特性完成。',
      },
      {
        question: 'var关键字的正确描述是？',
        options: [
          'var是动态类型，运行时确定类型',
          'var可以用于字段和方法参数',
          'var是局部变量类型推断，编译时确定类型',
          'var可以不初始化直接声明',
        ],
        correct: 2,
        explanation:
          'var是局部变量类型推断，编译器在编译时根据初始化表达式确定具体类型，保持Java的静态类型特性。',
      },
      {
        question: '以下哪个是LTS版本？',
        options: [
          'Java 9, Java 12, Java 15',
          'Java 8, Java 11, Java 17, Java 21',
          'Java 10, Java 13, Java 16',
          '所有版本都是LTS',
        ],
        correct: 1,
        explanation:
          'LTS（长期支持）版本包括Java 8、Java 11、Java 17和Java 21，每2-3年发布一个LTS版本。',
      },
      {
        question: 'Java 11的主要新特性不包括？',
        options: [
          'HTTP/2客户端',
          '单文件程序执行',
          'String新方法（isBlank、strip等）',
          'Lambda表达式',
        ],
        correct: 3,
        explanation:
          'Lambda表达式是Java 8的特性，Java 11的主要新特性包括HTTP/2客户端、单文件程序执行和String API增强。',
      },
    ]

    const selectAnswer = (answerIndex: number) => {
      selectedAnswers.value[currentQuestion.value] = answerIndex
    }

    const nextQuestion = () => {
      if (currentQuestion.value < questions.length - 1) {
        currentQuestion.value++
      } else {
        showResults.value = true
      }
    }

    const prevQuestion = () => {
      if (currentQuestion.value > 0) {
        currentQuestion.value--
      }
    }

    const resetQuiz = () => {
      currentQuestion.value = 0
      selectedAnswers.value = []
      showResults.value = false
    }

    const getScore = () => {
      return selectedAnswers.value.reduce((score, answer, index) => {
        return score + (answer === questions[index].correct ? 1 : 0)
      }, 0)
    }

    const getScorePercentage = () => {
      return Math.round((getScore() / questions.length) * 100)
    }

    return () =>
      h('div', { class: 'interactive-playground' }, [
        h('h4', '🧠 第一章知识测验'),

        !showResults.value
          ? [
              // 问题显示
              h(
                'div',
                {
                  style:
                    'background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin-bottom: 1rem;',
                },
                [
                  h(
                    'div',
                    {
                      style:
                        'display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;',
                    },
                    [
                      h(
                        'span',
                        { style: 'color: #667eea; font-weight: 500;' },
                        `问题 ${currentQuestion.value + 1} / ${questions.length}`,
                      ),
                      h(
                        'div',
                        {
                          style:
                            'background: #f0f4ff; padding: 0.5rem 1rem; border-radius: 20px; color: #667eea; font-size: 0.9rem;',
                        },
                        `已完成: ${selectedAnswers.value.filter((a) => a !== undefined).length}/${questions.length}`,
                      ),
                    ],
                  ),
                  h(
                    'h3',
                    { style: 'color: #333; margin-bottom: 1.5rem; line-height: 1.4;' },
                    questions[currentQuestion.value].question,
                  ),
                  h('div', { style: 'display: flex; flex-direction: column; gap: 0.75rem;' }, [
                    questions[currentQuestion.value].options.map((option, index) =>
                      h(
                        'button',
                        {
                          style: `padding: 1rem; text-align: left; border: 2px solid ${selectedAnswers.value[currentQuestion.value] === index ? '#667eea' : '#e9ecef'}; border-radius: 8px; background: ${selectedAnswers.value[currentQuestion.value] === index ? '#f0f4ff' : 'white'}; cursor: pointer; transition: all 0.2s ease;`,
                          onClick: () => selectAnswer(index),
                          onMouseover: (e: any) => {
                            if (selectedAnswers.value[currentQuestion.value] !== index) {
                              e.target.style.borderColor = '#667eea'
                              e.target.style.background = '#f8f9fa'
                            }
                          },
                          onMouseleave: (e: any) => {
                            if (selectedAnswers.value[currentQuestion.value] !== index) {
                              e.target.style.borderColor = '#e9ecef'
                              e.target.style.background = 'white'
                            }
                          },
                        },
                        [
                          h(
                            'span',
                            { style: 'color: #333; line-height: 1.5;' },
                            `${String.fromCharCode(65 + index)}. ${option}`,
                          ),
                        ],
                      ),
                    ),
                  ]),
                ],
              ),

              // 导航按钮
              h('div', { class: 'demo-controls' }, [
                h(
                  'button',
                  {
                    onClick: prevQuestion,
                    disabled: currentQuestion.value === 0,
                    class: 'control-button',
                  },
                  '⬅️ 上一题',
                ),
                h(
                  'button',
                  {
                    onClick: resetQuiz,
                    class: 'control-button',
                  },
                  '🔄 重新开始',
                ),
                h(
                  'button',
                  {
                    onClick: nextQuestion,
                    disabled: selectedAnswers.value[currentQuestion.value] === undefined,
                    class: 'control-button',
                  },
                  currentQuestion.value === questions.length - 1 ? '📊 查看结果' : '下一题 ➡️',
                ),
              ]),
            ]
          : [
              // 结果显示
              h(
                'div',
                {
                  style:
                    'background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);',
                },
                [
                  h('div', { style: 'text-align: center; margin-bottom: 2rem;' }, [
                    h('h3', { style: 'color: #333; margin-bottom: 1rem;' }, '🎉 测验完成！'),
                    h(
                      'div',
                      {
                        style: `font-size: 3rem; font-weight: bold; color: ${getScorePercentage() >= 80 ? '#4caf50' : getScorePercentage() >= 60 ? '#ff9800' : '#f44336'}; margin-bottom: 0.5rem;`,
                      },
                      `${getScorePercentage()}%`,
                    ),
                    h(
                      'p',
                      { style: 'color: #666; margin: 0;' },
                      `您答对了 ${getScore()} / ${questions.length} 题`,
                    ),
                  ]),

                  // 详细结果
                  h('div', { style: 'margin-bottom: 2rem;' }, [
                    h('h4', { style: 'color: #333; margin-bottom: 1rem;' }, '📋 详细结果'),
                    questions.map((question, index) =>
                      h(
                        'div',
                        {
                          style: `margin-bottom: 1rem; padding: 1rem; border-radius: 6px; border-left: 4px solid ${selectedAnswers.value[index] === question.correct ? '#4caf50' : '#f44336'}; background: ${selectedAnswers.value[index] === question.correct ? '#f1f8e9' : '#ffebee'};`,
                        },
                        [
                          h(
                            'div',
                            {
                              style:
                                'display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;',
                            },
                            [
                              h(
                                'span',
                                {
                                  style: `color: ${selectedAnswers.value[index] === question.correct ? '#4caf50' : '#f44336'};`,
                                },
                                selectedAnswers.value[index] === question.correct ? '✅' : '❌',
                              ),
                              h(
                                'span',
                                { style: 'font-weight: 500; color: #333;' },
                                `问题 ${index + 1}`,
                              ),
                            ],
                          ),
                          h(
                            'p',
                            { style: 'margin: 0 0 0.5rem 0; color: #333; font-size: 0.9rem;' },
                            question.explanation,
                          ),
                        ],
                      ),
                    ),
                  ]),

                  h('div', { class: 'demo-controls' }, [
                    h(
                      'button',
                      {
                        onClick: resetQuiz,
                        class: 'control-button',
                      },
                      '🔄 重新测验',
                    ),
                  ]),
                ],
              ),
            ],
      ])
  },
})

const getInteractiveComponent = (type: string) => {
  const componentMap: Record<string, any> = {
    // 通用组件
    'code-comparison': CodeComparison,
    'concept-demo': ConceptDemo,
    'interactive-playground': InteractivePlayground,

    // 第1章 - Java基础
    'compilation-demo': CompilationDemo,
    'bytecode-analysis': BytecodeAnalysis,
    'jvm-architecture': JVMArchitecture,
    'release-timeline': ReleaseTimeline,
    'lts-comparison': LTSComparison,
    'feature-lifecycle': ConceptDemo,
    'var-examples': VarExamples,
    'type-inference': VarTypeInference,
    'best-practices': ConceptDemo,
    'evolution-timeline': ConceptDemo,
    'feature-states': ConceptDemo,
    'jep-process': ConceptDemo,
    'collection-factory-demo': CollectionFactoryDemo,
    'http-client-demo': InteractivePlayground,
    'string-methods-demo': CodeComparison,
    'knowledge-quiz': KnowledgeQuiz,
    'practice-exercises': InteractivePlayground,
    'chapter-review': ConceptDemo,

    // 第6章 - 并发编程
    'lock-demo': ConceptDemo,
    'atomic-demo': InteractivePlayground,
    'blocking-queue-demo': ConceptDemo,
    'future-demo': InteractivePlayground,
    'executor-demo': ConceptDemo,

    // 第7章 - 性能优化
    'memory-hierarchy-demo': ConceptDemo,
    'gc-process-demo': InteractivePlayground,
    'jit-demo': ConceptDemo,
    'jfr-recording-demo': InteractivePlayground,

    // 第8章 - JVM语言
    'language-comparison': CodeComparison,
    'pyramid-demo': ConceptDemo,
    'selection-checklist': InteractivePlayground,
    'bytecode-demo': CodeComparison,
    'risk-assessment': ConceptDemo,
    'interop-demo': CodeComparison,

    // 第9章 - Kotlin (已在 JavaChapter9.vue 中定义)
    'java-kotlin-converter': 'JavaKotlinConverter',
    'syntax-comparison': 'SyntaxComparison',
    'data-class-generator': 'DataClassGenerator',
    'null-safety-playground': 'NullSafetyPlayground',
    'coroutines-playground': 'CoroutinesPlayground',
  }

  return componentMap[type] || DefaultInteractive
}
</script>

<style scoped>
/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background 0.2s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 2rem;
  max-height: 70vh;
  overflow-y: auto;
}

/* 交互组件通用样式 */
.default-interactive {
  text-align: center;
  padding: 2rem;
}

.coming-soon {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  color: #666;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.code-comparison {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.example-tabs {
  display: flex;
  gap: 0.5rem;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 1rem;
}

.tab-button {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-button.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.comparison-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.code-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.code-section h4 {
  margin: 0;
  color: #333;
  font-size: 1rem;
}

.code-block {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  line-height: 1.4;
  margin: 0;
  white-space: pre-wrap;
}

.concept-demo {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.demo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.demo-header h4 {
  margin: 0;
  color: #333;
}

.step-indicator {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
}

.demo-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.control-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.control-button:hover:not(:disabled) {
  background: #5a6fd8;
}

.interactive-playground {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.code-editor {
  width: 100%;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  resize: vertical;
}

.run-button {
  background: #4caf50;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  align-self: flex-start;
}

.run-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.result-section h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.result-output {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  margin: 0;
  min-height: 100px;
  white-space: pre-wrap;
}
</style>
