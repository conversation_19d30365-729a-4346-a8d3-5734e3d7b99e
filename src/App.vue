<script setup lang="ts">
import { RouterView } from 'vue-router'
import NavigationBar from '@/components/NavigationBar.vue'
import FloatingChapterMenu from '@/components/FloatingChapterMenu.vue'
import BackToTopButton from '@/components/BackToTopButton.vue'
</script>

<template>
  <div id="app">
    <NavigationBar />
    <FloatingChapterMenu />
    <BackToTopButton />

    <main>
      <RouterView />
    </main>
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
}

main {
  min-height: calc(100vh - 80px);
  padding-top: 80px; /* 为固定导航栏留出空间 */
  background: #f8f9fa;
}
</style>
